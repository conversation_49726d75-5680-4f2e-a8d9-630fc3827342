'use client'

import Link from 'next/link'
import { 
  ArrowLeft,
  TrendingUp,
  TrendingDown,
  BarChart3,
  LineChart,
  PieChart,
  Calendar,
  Download,
  Filter,
  Cpu,
  MemoryStick,
  HardDrive,
  Network,
  Clock,
  Target,
  AlertTriangle,
  CheckCircle,
  Zap
} from 'lucide-react'

export default function PerformanceAnalysisPage() {
  // 性能趋势数据
  const performanceTrends = [
    {
      metric: 'CPU使用率',
      current: 68,
      previous: 72,
      change: -4,
      trend: 'down',
      status: 'improving',
      icon: Cpu,
      color: 'blue'
    },
    {
      metric: '内存使用率',
      current: 72,
      previous: 69,
      change: +3,
      trend: 'up',
      status: 'warning',
      icon: MemoryStick,
      color: 'purple'
    },
    {
      metric: '磁盘I/O',
      current: 45,
      previous: 48,
      change: -3,
      trend: 'down',
      status: 'improving',
      icon: HardDrive,
      color: 'green'
    },
    {
      metric: '网络延迟',
      current: 12,
      previous: 15,
      change: -3,
      trend: 'down',
      status: 'improving',
      icon: Network,
      color: 'orange'
    }
  ]

  // 性能排行榜
  const performanceRanking = [
    { name: 'Web服务器-03', score: 95, cpu: 25, memory: 45, disk: 30, status: 'excellent' },
    { name: '应用服务器-02', score: 92, cpu: 32, memory: 52, disk: 35, status: 'excellent' },
    { name: 'Web服务器-01', score: 88, cpu: 45, memory: 68, disk: 32, status: 'good' },
    { name: '应用服务器-01', score: 85, cpu: 23, memory: 45, disk: 28, status: 'good' },
    { name: '数据库服务器-02', score: 78, cpu: 65, memory: 78, disk: 45, status: 'warning' },
    { name: '数据库服务器-01', score: 65, cpu: 78, memory: 85, disk: 56, status: 'critical' }
  ]

  // 优化建议
  const optimizationSuggestions = [
    {
      type: '内存优化',
      priority: 'high',
      device: '数据库服务器-01',
      description: '内存使用率持续超过80%，建议增加内存或优化数据库查询',
      impact: '可提升15%性能',
      effort: '中等'
    },
    {
      type: 'CPU优化',
      priority: 'medium',
      device: '数据库服务器-01',
      description: 'CPU使用率峰值频繁，建议优化慢查询或增加CPU核心',
      impact: '可提升12%性能',
      effort: '较高'
    },
    {
      type: '磁盘优化',
      priority: 'medium',
      device: '文件服务器-01',
      description: '磁盘使用率接近90%，建议清理无用文件或扩容',
      impact: '可提升8%性能',
      effort: '较低'
    },
    {
      type: '网络优化',
      priority: 'low',
      device: '全部服务器',
      description: '网络延迟偶尔波动，建议检查网络配置',
      impact: '可提升5%性能',
      effort: '较低'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'text-green-600 bg-green-100'
      case 'good': return 'text-blue-600 bg-blue-100'
      case 'warning': return 'text-orange-600 bg-orange-100'
      case 'critical': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-100'
      case 'medium': return 'text-orange-600 bg-orange-100'
      case 'low': return 'text-blue-600 bg-blue-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="w-full px-4 py-8 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/monitoring" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-3">
              <TrendingUp className="w-8 h-8 text-green-600" />
              <span>性能分析</span>
            </h1>
            <p className="mt-1 text-gray-600">设备性能趋势分析，历史数据对比和性能优化建议</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
            <button className="px-3 py-1 text-sm font-medium bg-white text-gray-900 rounded shadow-sm">近7天</button>
            <button className="px-3 py-1 text-sm font-medium text-gray-600 hover:text-gray-900">近30天</button>
            <button className="px-3 py-1 text-sm font-medium text-gray-600 hover:text-gray-900">近90天</button>
          </div>
          <button className="btn-secondary flex items-center space-x-2">
            <Download className="w-4 h-4" />
            <span>导出报告</span>
          </button>
        </div>
      </div>

      {/* 性能趋势概览 */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">性能趋势概览</h2>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Clock className="w-4 h-4" />
            <span>数据更新时间: 2024-01-15 14:30</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {performanceTrends.map((trend, index) => {
            const Icon = trend.icon
            return (
              <div key={trend.metric} className="card">
                <div className="card-content">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`w-12 h-12 rounded-2xl flex items-center justify-center ${
                      trend.color === 'blue' ? 'bg-blue-100' :
                      trend.color === 'purple' ? 'bg-purple-100' :
                      trend.color === 'green' ? 'bg-green-100' : 'bg-orange-100'
                    }`}>
                      <Icon className={`w-6 h-6 ${
                        trend.color === 'blue' ? 'text-blue-600' :
                        trend.color === 'purple' ? 'text-purple-600' :
                        trend.color === 'green' ? 'text-green-600' : 'text-orange-600'
                      }`} />
                    </div>
                    <div className="flex items-center space-x-1">
                      {trend.trend === 'up' ? (
                        <TrendingUp className={`w-4 h-4 ${trend.status === 'warning' ? 'text-red-500' : 'text-green-500'}`} />
                      ) : (
                        <TrendingDown className="w-4 h-4 text-green-500" />
                      )}
                      <span className={`text-sm font-medium ${
                        trend.status === 'improving' ? 'text-green-600' : 'text-orange-600'
                      }`}>
                        {trend.change > 0 ? '+' : ''}{trend.change}%
                      </span>
                    </div>
                  </div>
                  
                  <div className="text-sm font-medium text-gray-600 mb-1">{trend.metric}</div>
                  <div className="text-2xl font-bold text-gray-900 mb-2">
                    {trend.current}{trend.metric.includes('延迟') ? 'ms' : '%'}
                  </div>
                  
                  <div className="flex items-center justify-between text-xs">
                    <span className="text-gray-500">上期: {trend.previous}{trend.metric.includes('延迟') ? 'ms' : '%'}</span>
                    <span className={`px-2 py-1 rounded-full font-medium ${
                      trend.status === 'improving' ? 'bg-green-100 text-green-700' : 'bg-orange-100 text-orange-700'
                    }`}>
                      {trend.status === 'improving' ? '改善' : '需关注'}
                    </span>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </section>

      {/* 性能排行榜 */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">设备性能排行榜</h2>
          <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
            查看完整排行 →
          </button>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="space-y-4">
              {performanceRanking.map((device, index) => (
                <div key={device.name} className="flex items-center justify-between p-4 bg-gray-50/50 rounded-xl hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-4">
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm ${
                      index === 0 ? 'bg-yellow-100 text-yellow-700' :
                      index === 1 ? 'bg-gray-100 text-gray-700' :
                      index === 2 ? 'bg-orange-100 text-orange-700' : 'bg-blue-100 text-blue-700'
                    }`}>
                      {index + 1}
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">{device.name}</h4>
                      <div className="flex items-center space-x-4 text-xs text-gray-600">
                        <span>CPU: {device.cpu}%</span>
                        <span>内存: {device.memory}%</span>
                        <span>磁盘: {device.disk}%</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4">
                    <div className="text-right">
                      <div className="text-2xl font-bold text-gray-900">{device.score}</div>
                      <div className="text-xs text-gray-500">性能分数</div>
                    </div>
                    <span className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusColor(device.status)}`}>
                      {device.status === 'excellent' ? '优秀' :
                       device.status === 'good' ? '良好' :
                       device.status === 'warning' ? '一般' : '需优化'}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* 优化建议 */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">性能优化建议</h2>
          <div className="flex items-center space-x-2">
            <Zap className="w-5 h-5 text-yellow-500" />
            <span className="text-sm text-gray-600">基于AI分析生成</span>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {optimizationSuggestions.map((suggestion, index) => (
            <div key={index} className="card">
              <div className="card-content">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <Target className="w-6 h-6 text-blue-600" />
                    <div>
                      <h4 className="font-semibold text-gray-900">{suggestion.type}</h4>
                      <p className="text-sm text-gray-600">{suggestion.device}</p>
                    </div>
                  </div>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(suggestion.priority)}`}>
                    {suggestion.priority === 'high' ? '高优先级' :
                     suggestion.priority === 'medium' ? '中优先级' : '低优先级'}
                  </span>
                </div>
                
                <p className="text-sm text-gray-700 mb-4">{suggestion.description}</p>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">预期收益:</span>
                    <div className="font-medium text-green-600">{suggestion.impact}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">实施难度:</span>
                    <div className="font-medium text-gray-900">{suggestion.effort}</div>
                  </div>
                </div>
                
                <div className="mt-4 pt-4 border-t border-gray-100">
                  <button className="w-full btn-primary text-sm py-2">
                    查看详细方案
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* 性能图表区域 */}
      <section className="space-y-6">
        <h2 className="text-xl font-bold text-gray-900">性能趋势图表</h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                <LineChart className="w-5 h-5 text-blue-600" />
                <span>CPU使用率趋势</span>
              </h3>
            </div>
            <div className="card-content">
              <div className="h-64 bg-gray-50 rounded-xl flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <BarChart3 className="w-12 h-12 mx-auto mb-2" />
                  <p>图表组件将在此处显示</p>
                </div>
              </div>
            </div>
          </div>
          
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                <PieChart className="w-5 h-5 text-purple-600" />
                <span>资源使用分布</span>
              </h3>
            </div>
            <div className="card-content">
              <div className="h-64 bg-gray-50 rounded-xl flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <PieChart className="w-12 h-12 mx-auto mb-2" />
                  <p>图表组件将在此处显示</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

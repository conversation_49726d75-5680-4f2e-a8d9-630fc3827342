'use client'

import Link from 'next/link'
import { usePathname } from 'next/navigation'
import {
  LayoutDashboard,
  Monitor,
  FileText,
  Database,
  GitMerge,
  Shield,
  HardDrive,
  Server,
  Network,
  Settings,
  Sparkles
} from 'lucide-react'

const navigation = [
  { name: '总览', href: '/dashboard', icon: LayoutDashboard },
  { name: '数据大屏', href: '/dashboard/screen', icon: Monitor },
  { name: '报表分析', href: '/dashboard/reports', icon: FileText },
  { name: '数据采集', href: '/dashboard/collection', icon: Database },
  { name: '数据汇聚', href: '/dashboard/aggregation', icon: GitMerge },
  { name: '数据治理', href: '/dashboard/governance', icon: Shield },
  { name: '资源池管理', href: '/dashboard/resources', icon: HardDrive },
  { name: '设备监控', href: '/dashboard/monitoring', icon: Server },
  { name: '网络管理', href: '/dashboard/network', icon: Network },
  { name: '设计演示', href: '/dashboard/demo', icon: Sparkles },
  { name: '系统设置', href: '/dashboard/settings', icon: Settings },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="flex flex-col w-72 bg-white/80 backdrop-blur-xl shadow-glass border-r border-white/20">
      {/* Logo */}
      <div className="flex items-center justify-center h-20 px-6 bg-gradient-to-r from-blue-600 to-indigo-600 relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 animate-shimmer"></div>
        <div className="relative z-10 flex items-center space-x-3">
          <div className="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center backdrop-blur-sm">
            <div className="w-6 h-6 bg-white rounded-lg"></div>
          </div>
          <div>
            <h1 className="text-xl font-bold text-white">云宇政数</h1>
            <p className="text-xs text-blue-100">数据管理平台</p>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-3 py-8 space-y-1">
        {navigation.map((item, index) => {
          const isActive = pathname === item.href
          const Icon = item.icon

          return (
            <Link
              key={item.name}
              href={item.href}
              className={`nav-item ${isActive ? 'active' : ''} animate-slide-in-left`}
              style={{ animationDelay: `${index * 50}ms` }}
            >
              <div className="flex items-center space-x-3 relative z-10">
                <div className={`p-2 rounded-lg transition-all duration-300 ${
                  isActive
                    ? 'bg-blue-500/20 text-blue-600'
                    : 'bg-gray-100/50 text-gray-500 group-hover:bg-blue-500/10 group-hover:text-blue-600'
                }`}>
                  <Icon className="w-5 h-5" />
                </div>
                <span className="font-medium">{item.name}</span>
              </div>
              {isActive && (
                <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-blue-500 to-indigo-500 rounded-l-full"></div>
              )}
            </Link>
          )
        })}
      </nav>

      {/* User Profile */}
      <div className="p-4 border-t border-gray-100/50">
        <div className="flex items-center space-x-3 p-3 rounded-xl bg-gradient-to-r from-gray-50 to-blue-50/50 hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 cursor-pointer">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center text-white font-bold">
            管
          </div>
          <div className="flex-1">
            <div className="text-sm font-medium text-gray-900">管理员</div>
            <div className="text-xs text-gray-500">系统管理员</div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 text-center">
        <div className="text-xs text-gray-400 bg-gray-50/50 rounded-lg py-2 px-3">
          版本 1.0.0 • 现代化界面
        </div>
      </div>
    </div>
  )
}

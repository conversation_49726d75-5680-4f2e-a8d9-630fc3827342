export default function DataGovernancePage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">数据治理</h1>
          <p className="mt-1 text-gray-600">数据质量管控与清洗治理</p>
        </div>
        <button className="btn-primary">
          新建治理规则
        </button>
      </div>

      {/* 数据质量概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[
          { label: '数据质量评分', value: '92', unit: '分', color: 'success' },
          { label: '完整性', value: '95.2', unit: '%', color: 'success' },
          { label: '准确性', value: '88.7', unit: '%', color: 'warning' },
          { label: '一致性', value: '91.5', unit: '%', color: 'success' },
        ].map((metric) => (
          <div key={metric.label} className="card">
            <div className="card-content">
              <div className="text-center">
                <div className={`text-2xl font-bold ${
                  metric.color === 'success' ? 'text-success-600' :
                  metric.color === 'warning' ? 'text-warning-600' :
                  'text-error-600'
                }`}>
                  {metric.value}
                  <span className="text-sm text-gray-500 ml-1">{metric.unit}</span>
                </div>
                <div className="text-sm text-gray-600 mt-1">{metric.label}</div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 治理规则和任务 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 治理规则 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">治理规则</h3>
          </div>
          <div className="card-content">
            <div className="space-y-3">
              {[
                { name: '身份证号格式校验', type: '格式校验', status: '启用', coverage: '98%' },
                { name: '手机号码标准化', type: '数据清洗', status: '启用', coverage: '100%' },
                { name: '地址信息去重', type: '重复检测', status: '启用', coverage: '95%' },
                { name: '日期格式统一', type: '格式转换', status: '停用', coverage: '0%' },
              ].map((rule, index) => (
                <div key={index} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  <div>
                    <div className="font-medium text-gray-900">{rule.name}</div>
                    <div className="text-sm text-gray-500">{rule.type}</div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className="text-sm text-gray-600">覆盖率: {rule.coverage}</span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      rule.status === '启用' 
                        ? 'bg-success-100 text-success-700' 
                        : 'bg-gray-100 text-gray-700'
                    }`}>
                      {rule.status}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 清洗任务 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">清洗任务</h3>
          </div>
          <div className="card-content">
            <div className="space-y-3">
              {[
                { name: '人口数据清洗', progress: 100, status: '已完成', time: '2小时前' },
                { name: '企业信息标准化', progress: 75, status: '进行中', time: '正在运行' },
                { name: '地址数据去重', progress: 0, status: '等待中', time: '计划中' },
                { name: '历史数据修复', progress: 45, status: '进行中', time: '正在运行' },
              ].map((task, index) => (
                <div key={index} className="p-3 border border-gray-200 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <div className="font-medium text-gray-900">{task.name}</div>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      task.status === '已完成' ? 'bg-success-100 text-success-700' :
                      task.status === '进行中' ? 'bg-primary-100 text-primary-700' :
                      'bg-gray-100 text-gray-700'
                    }`}>
                      {task.status}
                    </span>
                  </div>
                  
                  {task.status === '进行中' && (
                    <div className="mb-2">
                      <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                        <span>进度</span>
                        <span>{task.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${task.progress}%` }}
                        ></div>
                      </div>
                    </div>
                  )}
                  
                  <div className="text-sm text-gray-500">{task.time}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

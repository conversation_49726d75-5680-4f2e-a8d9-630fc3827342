'use client'

import { Database, Search, Filter, BarChart3, Download, Play, Pause, Settings } from 'lucide-react'

export default function DataServicesPage() {
  const dataServices = [
    {
      id: 1,
      name: '人口数据查询服务',
      description: '提供人口基础信息的实时查询服务',
      status: 'running',
      endpoint: '/api/v1/population/query',
      qps: 156,
      avgResponse: '95ms',
      successRate: '99.9%',
      dailyRequests: 12450,
      features: ['实时查询', '批量查询', '模糊搜索', '数据导出']
    },
    {
      id: 2,
      name: '企业信息查询服务',
      description: '企业工商注册信息查询和验证服务',
      status: 'running',
      endpoint: '/api/v1/enterprise/query',
      qps: 89,
      avgResponse: '120ms',
      successRate: '99.5%',
      dailyRequests: 8900,
      features: ['信息查询', '状态验证', '历史记录', '变更通知']
    },
    {
      id: 3,
      name: '地理数据分析服务',
      description: '地理空间数据分析和可视化服务',
      status: 'maintenance',
      endpoint: '/api/v1/geo/analysis',
      qps: 23,
      avgResponse: '234ms',
      successRate: '98.8%',
      dailyRequests: 2300,
      features: ['空间分析', '路径规划', '区域统计', '热力图']
    },
    {
      id: 4,
      name: '数据清洗服务',
      description: '自动化数据清洗和质量检测服务',
      status: 'running',
      endpoint: '/api/v1/data/cleaning',
      qps: 45,
      avgResponse: '180ms',
      successRate: '99.2%',
      dailyRequests: 4500,
      features: ['重复检测', '格式标准化', '异常识别', '质量评分']
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-700'
      case 'maintenance': return 'bg-yellow-100 text-yellow-700'
      case 'stopped': return 'bg-red-100 text-red-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'running': return '运行中'
      case 'maintenance': return '维护中'
      case 'stopped': return '已停止'
      default: return '未知'
    }
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">数据服务</h1>
        <p className="text-xl text-gray-600">管理和监控数据查询、分析和处理服务</p>
      </div>

      {/* 服务概览统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">运行服务</p>
              <p className="text-3xl font-bold text-gray-900">3</p>
            </div>
            <Database className="w-8 h-8 text-green-500" />
          </div>
        </div>
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">总QPS</p>
              <p className="text-3xl font-bold text-gray-900">313</p>
            </div>
            <BarChart3 className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">日请求量</p>
              <p className="text-3xl font-bold text-gray-900">28.1K</p>
            </div>
            <Search className="w-8 h-8 text-purple-500" />
          </div>
        </div>
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">平均成功率</p>
              <p className="text-3xl font-bold text-gray-900">99.5%</p>
            </div>
            <BarChart3 className="w-8 h-8 text-indigo-500" />
          </div>
        </div>
      </div>

      {/* 服务列表 */}
      <div className="space-y-6">
        {dataServices.map((service) => (
          <div
            key={service.id}
            className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden"
          >
            {/* 服务头部 */}
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-xl font-bold text-gray-900">{service.name}</h3>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(service.status)}`}>
                      {getStatusText(service.status)}
                    </span>
                  </div>
                  <p className="text-gray-600 mb-4">{service.description}</p>
                  <div className="flex items-center space-x-6 text-sm text-gray-600">
                    <span className="bg-gray-100 px-3 py-1 rounded-full font-mono">
                      {service.endpoint}
                    </span>
                  </div>
                </div>
                <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-xl flex items-center justify-center">
                  <Database className="w-8 h-8 text-white" />
                </div>
              </div>
            </div>

            {/* 服务指标 */}
            <div className="p-6 border-b border-gray-100">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">性能指标</h4>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="bg-gray-50/50 rounded-xl p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <BarChart3 className="w-5 h-5 text-blue-500" />
                    <h5 className="font-medium text-gray-900">QPS</h5>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{service.qps}</p>
                </div>
                <div className="bg-gray-50/50 rounded-xl p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <BarChart3 className="w-5 h-5 text-green-500" />
                    <h5 className="font-medium text-gray-900">平均响应</h5>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{service.avgResponse}</p>
                </div>
                <div className="bg-gray-50/50 rounded-xl p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <BarChart3 className="w-5 h-5 text-purple-500" />
                    <h5 className="font-medium text-gray-900">成功率</h5>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{service.successRate}</p>
                </div>
                <div className="bg-gray-50/50 rounded-xl p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Search className="w-5 h-5 text-orange-500" />
                    <h5 className="font-medium text-gray-900">日请求</h5>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{service.dailyRequests.toLocaleString()}</p>
                </div>
              </div>
            </div>

            {/* 服务功能 */}
            <div className="p-6 border-b border-gray-100">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">服务功能</h4>
              <div className="flex flex-wrap gap-2">
                {service.features.map((feature) => (
                  <span key={feature} className="text-sm bg-blue-100 text-blue-600 px-3 py-1 rounded-full">
                    {feature}
                  </span>
                ))}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="px-6 py-4 bg-gray-50/30">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <button className="text-indigo-600 hover:text-indigo-700 font-medium text-sm transition-colors">
                    查看详情
                  </button>
                  <button className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors">
                    API文档
                  </button>
                  <button className="text-green-600 hover:text-green-700 font-medium text-sm transition-colors">
                    在线测试
                  </button>
                </div>
                <div className="flex items-center space-x-2">
                  {service.status === 'running' ? (
                    <button className="flex items-center space-x-1 text-red-600 hover:text-red-700 font-medium text-sm transition-colors">
                      <Pause className="w-4 h-4" />
                      <span>停止</span>
                    </button>
                  ) : (
                    <button className="flex items-center space-x-1 text-green-600 hover:text-green-700 font-medium text-sm transition-colors">
                      <Play className="w-4 h-4" />
                      <span>启动</span>
                    </button>
                  )}
                  <button className="flex items-center space-x-1 text-gray-600 hover:text-gray-700 font-medium text-sm transition-colors">
                    <Settings className="w-4 h-4" />
                    <span>配置</span>
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

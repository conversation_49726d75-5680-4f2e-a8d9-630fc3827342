'use client'

import { HardDrive, Folder, Server, Shield, ChevronDown } from 'lucide-react'
import { useState } from 'react'
import Link from 'next/link'

export default function ResourcesSystemLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [activeMenu, setActiveMenu] = useState<string | null>(null)

  const menuItems = [
    {
      id: 'directory',
      label: '资源目录',
      icon: Folder,
      href: '/resources/directory',
      subItems: [
        { label: '数据目录', href: '/resources/directory/data' },
        { label: '文件目录', href: '/resources/directory/files' },
        { label: '接口目录', href: '/resources/directory/apis' },
      ]
    },
    {
      id: 'services',
      label: '资源服务',
      icon: Server,
      href: '/resources/services',
      subItems: [
        { label: '数据服务', href: '/resources/services/data' },
        { label: '计算服务', href: '/resources/services/compute' },
        { label: '存储服务', href: '/resources/services/storage' },
      ]
    },
    {
      id: 'permissions',
      label: '权限控制',
      icon: Shield,
      href: '/resources/permissions',
      subItems: [
        { label: '用户权限', href: '/resources/permissions/users' },
        { label: '角色管理', href: '/resources/permissions/roles' },
        { label: '访问控制', href: '/resources/permissions/access' },
      ]
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-blue-50 to-indigo-100">
      {/* 系统头部 */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50">
        <div className="w-full px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              {/* 系统Logo */}
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-2xl flex items-center justify-center shadow-lg">
                  <HardDrive className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-600 to-blue-600 bg-clip-text text-transparent">
                    💾 资源池管理系统
                  </h1>
                  <p className="text-sm text-gray-600">数据资源统一管理与权限控制平台</p>
                </div>
              </div>

              {/* 导航菜单 */}
              <nav className="flex items-center space-x-6">
                {menuItems.map((item) => {
                  const Icon = item.icon
                  return (
                    <div
                      key={item.id}
                      className="relative"
                      onMouseEnter={() => setActiveMenu(item.id)}
                      onMouseLeave={() => setActiveMenu(null)}
                    >
                      <Link
                        href={item.href}
                        className="flex items-center space-x-2 px-4 py-2 rounded-lg text-gray-700 hover:text-indigo-600 hover:bg-indigo-50/50 transition-all duration-200 font-medium"
                      >
                        <Icon className="w-5 h-5" />
                        <span>{item.label}</span>
                        <ChevronDown className="w-4 h-4" />
                      </Link>

                      {/* 下拉菜单 */}
                      {activeMenu === item.id && (
                        <div className="absolute top-full left-0 mt-1 w-48 bg-white/95 backdrop-blur-sm rounded-xl shadow-lg border border-white/20 py-2 z-50">
                          {item.subItems.map((subItem) => (
                            <Link
                              key={subItem.href}
                              href={subItem.href}
                              className="block px-4 py-2 text-sm text-gray-700 hover:text-indigo-600 hover:bg-indigo-50/50 transition-colors"
                            >
                              {subItem.label}
                            </Link>
                          ))}
                        </div>
                      )}
                    </div>
                  )
                })}
              </nav>
            </div>

            {/* 返回主平台 */}
            <div className="flex items-center space-x-4">
              <a
                href="/"
                className="text-gray-600 hover:text-indigo-600 transition-colors text-sm font-medium"
              >
                返回首页
              </a>
              <a
                href="/dashboard"
                className="bg-indigo-600 text-white px-4 py-2 rounded-xl hover:bg-indigo-700 transition-colors text-sm font-medium"
              >
                主控台
              </a>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="relative z-10">
        {children}
      </main>

      {/* 背景装饰 */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-indigo-400/20 to-blue-400/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>
    </div>
  )
}

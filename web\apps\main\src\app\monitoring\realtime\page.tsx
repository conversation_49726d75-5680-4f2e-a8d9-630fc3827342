'use client'

import Link from 'next/link'
import { 
  ArrowLeft,
  Activity, 
  Cpu,
  MemoryStick,
  HardDrive,
  Network,
  Thermometer,
  Zap,
  RefreshCw,
  Pause,
  Play,
  Settings,
  Maximize2,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react'

export default function RealtimeMonitoringPage() {
  // 实时设备数据
  const realtimeDevices = [
    {
      id: 1,
      name: 'Web服务器-01',
      ip: '************',
      status: 'online',
      cpu: { value: 45, trend: 'up', status: 'normal' },
      memory: { value: 68, trend: 'stable', status: 'warning' },
      disk: { value: 32, trend: 'up', status: 'normal' },
      network: { in: 125, out: 89, trend: 'up', status: 'normal' },
      temperature: { value: 42, status: 'normal' },
      uptime: '15天 8小时'
    },
    {
      id: 2,
      name: '数据库服务器-01',
      ip: '************',
      status: 'online',
      cpu: { value: 78, trend: 'up', status: 'warning' },
      memory: { value: 85, trend: 'up', status: 'critical' },
      disk: { value: 56, trend: 'stable', status: 'normal' },
      network: { in: 256, out: 178, trend: 'up', status: 'normal' },
      temperature: { value: 58, status: 'warning' },
      uptime: '22天 14小时'
    },
    {
      id: 3,
      name: '应用服务器-01',
      ip: '************',
      status: 'online',
      cpu: { value: 23, trend: 'down', status: 'normal' },
      memory: { value: 45, trend: 'stable', status: 'normal' },
      disk: { value: 28, trend: 'up', status: 'normal' },
      network: { in: 89, out: 67, trend: 'stable', status: 'normal' },
      temperature: { value: 38, status: 'normal' },
      uptime: '8天 3小时'
    },
    {
      id: 4,
      name: '文件服务器-01',
      ip: '************',
      status: 'warning',
      cpu: { value: 12, trend: 'stable', status: 'normal' },
      memory: { value: 34, trend: 'down', status: 'normal' },
      disk: { value: 89, trend: 'up', status: 'critical' },
      network: { in: 45, out: 23, trend: 'down', status: 'normal' },
      temperature: { value: 35, status: 'normal' },
      uptime: '45天 12小时'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'normal': return 'text-green-600 bg-green-100'
      case 'warning': return 'text-orange-600 bg-orange-100'
      case 'critical': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="w-3 h-3 text-red-500" />
      case 'down': return <TrendingDown className="w-3 h-3 text-green-500" />
      default: return <div className="w-3 h-1 bg-gray-400 rounded"></div>
    }
  }

  return (
    <div className="w-full px-4 py-8 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/monitoring" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-3">
              <Activity className="w-8 h-8 text-blue-600" />
              <span>实时监控</span>
            </h1>
            <p className="mt-1 text-gray-600">设备实时状态监控，性能指标实时展示</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 bg-green-50 px-3 py-2 rounded-lg">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-green-700">实时更新中</span>
          </div>
          <button className="btn-secondary flex items-center space-x-2">
            <Pause className="w-4 h-4" />
            <span>暂停更新</span>
          </button>
          <button className="btn-secondary flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>设置</span>
          </button>
        </div>
      </div>

      {/* 实时概览指标 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[
          { label: '在线设备', value: '142/156', icon: CheckCircle, color: 'green' },
          { label: '平均CPU使用率', value: '52%', icon: Cpu, color: 'blue' },
          { label: '平均内存使用率', value: '58%', icon: MemoryStick, color: 'purple' },
          { label: '网络总流量', value: '1.2GB/s', icon: Network, color: 'orange' }
        ].map((metric, index) => {
          const Icon = metric.icon
          return (
            <div key={metric.label} className="card">
              <div className="card-content">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-600">{metric.label}</div>
                    <div className="text-2xl font-bold text-gray-900 mt-1">{metric.value}</div>
                  </div>
                  <div className={`w-12 h-12 rounded-2xl flex items-center justify-center ${
                    metric.color === 'green' ? 'bg-green-100' :
                    metric.color === 'blue' ? 'bg-blue-100' :
                    metric.color === 'purple' ? 'bg-purple-100' : 'bg-orange-100'
                  }`}>
                    <Icon className={`w-6 h-6 ${
                      metric.color === 'green' ? 'text-green-600' :
                      metric.color === 'blue' ? 'text-blue-600' :
                      metric.color === 'purple' ? 'text-purple-600' : 'text-orange-600'
                    }`} />
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 实时设备监控列表 */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">设备实时状态</h3>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-500">
                <RefreshCw className="w-4 h-4 animate-spin" />
                <span>每5秒更新</span>
              </div>
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                全屏查看
              </button>
            </div>
          </div>
        </div>
        <div className="card-content">
          <div className="space-y-6">
            {realtimeDevices.map((device) => (
              <div key={device.id} className="bg-gray-50/50 rounded-2xl p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-4">
                    <div className={`w-4 h-4 rounded-full ${
                      device.status === 'online' ? 'bg-green-500' :
                      device.status === 'warning' ? 'bg-orange-500' : 'bg-red-500'
                    }`}></div>
                    <div>
                      <h4 className="text-lg font-semibold text-gray-900">{device.name}</h4>
                      <p className="text-sm text-gray-600">{device.ip} • 运行时间: {device.uptime}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`px-3 py-1 text-xs font-medium rounded-full ${
                      device.status === 'online' ? 'bg-green-100 text-green-700' :
                      device.status === 'warning' ? 'bg-orange-100 text-orange-700' : 'bg-red-100 text-red-700'
                    }`}>
                      {device.status === 'online' ? '在线' :
                       device.status === 'warning' ? '警告' : '离线'}
                    </span>
                    <button className="text-gray-400 hover:text-gray-600">
                      <Maximize2 className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
                  {/* CPU */}
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <Cpu className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium text-gray-700">CPU</span>
                      {getTrendIcon(device.cpu.trend)}
                    </div>
                    <div className="text-2xl font-bold text-gray-900 mb-1">{device.cpu.value}%</div>
                    <div className={`text-xs px-2 py-1 rounded-full ${getStatusColor(device.cpu.status)}`}>
                      {device.cpu.status === 'normal' ? '正常' :
                       device.cpu.status === 'warning' ? '警告' : '严重'}
                    </div>
                    <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-1000 ${
                          device.cpu.status === 'normal' ? 'bg-green-500' :
                          device.cpu.status === 'warning' ? 'bg-orange-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${device.cpu.value}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* 内存 */}
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <MemoryStick className="w-4 h-4 text-purple-600" />
                      <span className="text-sm font-medium text-gray-700">内存</span>
                      {getTrendIcon(device.memory.trend)}
                    </div>
                    <div className="text-2xl font-bold text-gray-900 mb-1">{device.memory.value}%</div>
                    <div className={`text-xs px-2 py-1 rounded-full ${getStatusColor(device.memory.status)}`}>
                      {device.memory.status === 'normal' ? '正常' :
                       device.memory.status === 'warning' ? '警告' : '严重'}
                    </div>
                    <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-1000 ${
                          device.memory.status === 'normal' ? 'bg-green-500' :
                          device.memory.status === 'warning' ? 'bg-orange-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${device.memory.value}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* 磁盘 */}
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <HardDrive className="w-4 h-4 text-green-600" />
                      <span className="text-sm font-medium text-gray-700">磁盘</span>
                      {getTrendIcon(device.disk.trend)}
                    </div>
                    <div className="text-2xl font-bold text-gray-900 mb-1">{device.disk.value}%</div>
                    <div className={`text-xs px-2 py-1 rounded-full ${getStatusColor(device.disk.status)}`}>
                      {device.disk.status === 'normal' ? '正常' :
                       device.disk.status === 'warning' ? '警告' : '严重'}
                    </div>
                    <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-1000 ${
                          device.disk.status === 'normal' ? 'bg-green-500' :
                          device.disk.status === 'warning' ? 'bg-orange-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${device.disk.value}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* 网络 */}
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <Network className="w-4 h-4 text-orange-600" />
                      <span className="text-sm font-medium text-gray-700">网络</span>
                      {getTrendIcon(device.network.trend)}
                    </div>
                    <div className="text-sm font-bold text-gray-900 mb-1">
                      ↓{device.network.in}MB/s
                    </div>
                    <div className="text-sm font-bold text-gray-900 mb-1">
                      ↑{device.network.out}MB/s
                    </div>
                    <div className={`text-xs px-2 py-1 rounded-full ${getStatusColor(device.network.status)}`}>
                      {device.network.status === 'normal' ? '正常' :
                       device.network.status === 'warning' ? '警告' : '严重'}
                    </div>
                  </div>

                  {/* 温度 */}
                  <div className="text-center">
                    <div className="flex items-center justify-center space-x-2 mb-2">
                      <Thermometer className="w-4 h-4 text-red-600" />
                      <span className="text-sm font-medium text-gray-700">温度</span>
                    </div>
                    <div className="text-2xl font-bold text-gray-900 mb-1">{device.temperature.value}°C</div>
                    <div className={`text-xs px-2 py-1 rounded-full ${getStatusColor(device.temperature.status)}`}>
                      {device.temperature.status === 'normal' ? '正常' :
                       device.temperature.status === 'warning' ? '警告' : '过热'}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

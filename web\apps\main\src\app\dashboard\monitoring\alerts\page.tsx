'use client'

import Link from 'next/link'
import { 
  ArrowLeft,
  AlertTriangle,
  XCircle,
  CheckCircle,
  Clock,
  Bell,
  Settings,
  Filter,
  Search,
  Plus,
  Eye,
  Trash2,
  Edit,
  Play,
  Pause,
  RotateCcw,
  TrendingUp,
  TrendingDown,
  Calendar,
  User,
  Server,
  Cpu,
  MemoryStick,
  HardDrive,
  Network
} from 'lucide-react'

export default function AlertManagementPage() {
  // 告警统计
  const alertStats = [
    { label: '活跃告警', value: '13', change: '+2', changeType: 'increase', color: 'red' },
    { label: '今日新增', value: '8', change: '+3', changeType: 'increase', color: 'orange' },
    { label: '已处理', value: '25', change: '+12', changeType: 'increase', color: 'green' },
    { label: '处理率', value: '85%', change: '+5%', changeType: 'increase', color: 'blue' }
  ]

  // 告警列表
  const alerts = [
    {
      id: 1,
      title: 'CPU使用率过高',
      device: '数据库服务器-01',
      severity: 'critical',
      status: 'active',
      type: 'cpu',
      value: '89%',
      threshold: '80%',
      time: '2024-01-15 14:25:30',
      duration: '15分钟',
      description: 'CPU使用率持续超过阈值，可能影响系统性能'
    },
    {
      id: 2,
      title: '内存使用率警告',
      device: '数据库服务器-01',
      severity: 'warning',
      status: 'active',
      type: 'memory',
      value: '87%',
      threshold: '85%',
      time: '2024-01-15 14:20:15',
      duration: '20分钟',
      description: '内存使用率接近上限，建议检查内存泄漏'
    },
    {
      id: 3,
      title: '磁盘空间不足',
      device: '文件服务器-01',
      severity: 'warning',
      status: 'acknowledged',
      type: 'disk',
      value: '92%',
      threshold: '90%',
      time: '2024-01-15 13:45:22',
      duration: '1小时15分钟',
      description: '磁盘使用率超过90%，需要清理或扩容'
    },
    {
      id: 4,
      title: '网络延迟异常',
      device: 'Web服务器-02',
      severity: 'info',
      status: 'resolved',
      type: 'network',
      value: '85ms',
      threshold: '50ms',
      time: '2024-01-15 12:30:45',
      duration: '已解决',
      description: '网络延迟超过正常范围，已自动恢复'
    },
    {
      id: 5,
      title: '服务器离线',
      device: '应用服务器-03',
      severity: 'critical',
      status: 'active',
      type: 'server',
      value: '离线',
      threshold: '在线',
      time: '2024-01-15 11:15:10',
      duration: '3小时10分钟',
      description: '服务器无法连接，需要立即检查'
    }
  ]

  // 告警规则
  const alertRules = [
    {
      id: 1,
      name: 'CPU使用率监控',
      metric: 'CPU使用率',
      condition: '> 80%',
      severity: 'warning',
      enabled: true,
      devices: 'all'
    },
    {
      id: 2,
      name: 'CPU严重告警',
      metric: 'CPU使用率',
      condition: '> 90%',
      severity: 'critical',
      enabled: true,
      devices: 'all'
    },
    {
      id: 3,
      name: '内存使用率监控',
      metric: '内存使用率',
      condition: '> 85%',
      severity: 'warning',
      enabled: true,
      devices: 'all'
    },
    {
      id: 4,
      name: '磁盘空间监控',
      metric: '磁盘使用率',
      condition: '> 90%',
      severity: 'warning',
      enabled: true,
      devices: 'all'
    },
    {
      id: 5,
      name: '网络延迟监控',
      metric: '网络延迟',
      condition: '> 50ms',
      severity: 'info',
      enabled: false,
      devices: 'web-servers'
    }
  ]

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100'
      case 'warning': return 'text-orange-600 bg-orange-100'
      case 'info': return 'text-blue-600 bg-blue-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-red-600 bg-red-100'
      case 'acknowledged': return 'text-orange-600 bg-orange-100'
      case 'resolved': return 'text-green-600 bg-green-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'cpu': return <Cpu className="w-4 h-4" />
      case 'memory': return <MemoryStick className="w-4 h-4" />
      case 'disk': return <HardDrive className="w-4 h-4" />
      case 'network': return <Network className="w-4 h-4" />
      case 'server': return <Server className="w-4 h-4" />
      default: return <AlertTriangle className="w-4 h-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/monitoring" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-3">
              <AlertTriangle className="w-8 h-8 text-orange-600" />
              <span>告警管理</span>
            </h1>
            <p className="mt-1 text-gray-600">告警规则配置，告警历史查询和告警处理流程管理</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <button className="btn-secondary flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>告警设置</span>
          </button>
          <button className="btn-primary flex items-center space-x-2">
            <Plus className="w-4 h-4" />
            <span>新建规则</span>
          </button>
        </div>
      </div>

      {/* 告警统计 */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">告警统计</h2>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Clock className="w-4 h-4" />
            <span>实时更新</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {alertStats.map((stat, index) => (
            <div key={stat.label} className="card">
              <div className="card-content">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="text-sm text-gray-600">{stat.label}</div>
                    <div className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</div>
                    <div className="flex items-center space-x-1 mt-2">
                      {stat.changeType === 'increase' ? (
                        <TrendingUp className={`w-3 h-3 ${stat.color === 'green' ? 'text-green-500' : 'text-red-500'}`} />
                      ) : (
                        <TrendingDown className="w-3 h-3 text-green-500" />
                      )}
                      <span className={`text-xs font-medium ${
                        stat.color === 'green' ? 'text-green-600' : 
                        stat.changeType === 'increase' && stat.color !== 'green' ? 'text-red-600' : 'text-green-600'
                      }`}>
                        {stat.change}
                      </span>
                    </div>
                  </div>
                  <div className={`w-12 h-12 rounded-2xl flex items-center justify-center ${
                    stat.color === 'red' ? 'bg-red-100' :
                    stat.color === 'orange' ? 'bg-orange-100' :
                    stat.color === 'green' ? 'bg-green-100' : 'bg-blue-100'
                  }`}>
                    <Bell className={`w-6 h-6 ${
                      stat.color === 'red' ? 'text-red-600' :
                      stat.color === 'orange' ? 'text-orange-600' :
                      stat.color === 'green' ? 'text-green-600' : 'text-blue-600'
                    }`} />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* 活跃告警列表 */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">活跃告警</h2>
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="搜索告警..."
                className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <button className="btn-secondary flex items-center space-x-2">
              <Filter className="w-4 h-4" />
              <span>筛选</span>
            </button>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="space-y-4">
              {alerts.map((alert) => (
                <div key={alert.id} className="border border-gray-100 rounded-xl p-6 hover:bg-gray-50/50 transition-colors">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-4 flex-1">
                      <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                        alert.severity === 'critical' ? 'bg-red-100' :
                        alert.severity === 'warning' ? 'bg-orange-100' : 'bg-blue-100'
                      }`}>
                        {getTypeIcon(alert.type)}
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h4 className="font-semibold text-gray-900">{alert.title}</h4>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(alert.severity)}`}>
                            {alert.severity === 'critical' ? '严重' :
                             alert.severity === 'warning' ? '警告' : '信息'}
                          </span>
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(alert.status)}`}>
                            {alert.status === 'active' ? '活跃' :
                             alert.status === 'acknowledged' ? '已确认' : '已解决'}
                          </span>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600 mb-3">
                          <div>
                            <span className="font-medium">设备:</span> {alert.device}
                          </div>
                          <div>
                            <span className="font-medium">当前值:</span> {alert.value}
                          </div>
                          <div>
                            <span className="font-medium">阈值:</span> {alert.threshold}
                          </div>
                          <div>
                            <span className="font-medium">持续时间:</span> {alert.duration}
                          </div>
                        </div>
                        
                        <p className="text-sm text-gray-700 mb-3">{alert.description}</p>
                        
                        <div className="flex items-center space-x-2 text-xs text-gray-500">
                          <Calendar className="w-3 h-3" />
                          <span>{alert.time}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2 ml-4">
                      <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                        <CheckCircle className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors">
                        <RotateCcw className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* 告警规则管理 */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">告警规则</h2>
          <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
            查看所有规则 →
          </button>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">规则名称</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">监控指标</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">触发条件</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">严重程度</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">应用范围</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">状态</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {alertRules.map((rule) => (
                    <tr key={rule.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4 font-medium text-gray-900">{rule.name}</td>
                      <td className="py-3 px-4 text-gray-600">{rule.metric}</td>
                      <td className="py-3 px-4 text-gray-600">{rule.condition}</td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getSeverityColor(rule.severity)}`}>
                          {rule.severity === 'critical' ? '严重' :
                           rule.severity === 'warning' ? '警告' : '信息'}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        {rule.devices === 'all' ? '所有设备' : '指定设备'}
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          rule.enabled ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                        }`}>
                          {rule.enabled ? '启用' : '禁用'}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-2">
                          <button className="text-blue-600 hover:text-blue-700 text-sm">
                            <Edit className="w-4 h-4" />
                          </button>
                          <button className="text-gray-600 hover:text-gray-700 text-sm">
                            {rule.enabled ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                          </button>
                          <button className="text-red-600 hover:text-red-700 text-sm">
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

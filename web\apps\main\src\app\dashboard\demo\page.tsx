'use client'

import { useState } from 'react'
import { <PERSON>rk<PERSON>, Palette, Zap, <PERSON>, <PERSON>, <PERSON> } from 'lucide-react'

export default function DemoPage() {
  const [activeDemo, setActiveDemo] = useState('colors')

  const demos = [
    { id: 'colors', name: '色彩方案', icon: Palette },
    { id: 'animations', name: '动画效果', icon: Zap },
    { id: 'glass', name: '玻璃拟态', icon: Eye },
    { id: 'interactions', name: '交互体验', icon: Heart },
  ]

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div className="text-center animate-fade-in">
        <div className="inline-flex items-center space-x-3 mb-4">
          <Sparkles className="w-8 h-8 text-blue-600" />
          <h1 className="text-4xl font-bold gradient-text">现代化设计演示</h1>
        </div>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          体验全新的现代化界面设计，包含玻璃拟态、渐变色彩、流畅动画等前沿设计元素
        </p>
      </div>

      {/* 演示导航 */}
      <div className="flex justify-center space-x-4 animate-slide-up">
        {demos.map((demo) => {
          const Icon = demo.icon
          return (
            <button
              key={demo.id}
              onClick={() => setActiveDemo(demo.id)}
              className={`flex items-center space-x-2 px-6 py-3 rounded-2xl transition-all duration-300 ${
                activeDemo === demo.id
                  ? 'bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-glow'
                  : 'bg-white/80 backdrop-blur-sm text-gray-700 hover:bg-blue-50/50'
              }`}
            >
              <Icon className="w-5 h-5" />
              <span className="font-medium">{demo.name}</span>
            </button>
          )
        })}
      </div>

      {/* 演示内容 */}
      <div className="animate-slide-up" style={{ animationDelay: '200ms' }}>
        {activeDemo === 'colors' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 text-center">渐变色彩方案</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {[
                { name: '主色调', gradient: 'from-blue-500 to-indigo-500' },
                { name: '成功色', gradient: 'from-green-500 to-emerald-500' },
                { name: '警告色', gradient: 'from-orange-500 to-amber-500' },
                { name: '错误色', gradient: 'from-red-500 to-rose-500' },
                { name: '紫色系', gradient: 'from-purple-500 to-violet-500' },
                { name: '青色系', gradient: 'from-cyan-500 to-teal-500' },
                { name: '粉色系', gradient: 'from-pink-500 to-rose-500' },
                { name: '灰色系', gradient: 'from-gray-500 to-slate-500' },
              ].map((color) => (
                <div key={color.name} className="card hover:shadow-glow transition-all duration-300 group">
                  <div className="card-content text-center">
                    <div className={`w-16 h-16 bg-gradient-to-br ${color.gradient} rounded-2xl mx-auto mb-4 shadow-lg group-hover:shadow-glow transition-all duration-300 transform group-hover:scale-110`}></div>
                    <h3 className="font-semibold text-gray-900">{color.name}</h3>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeDemo === 'animations' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 text-center">动画效果展示</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="card hover:shadow-glow transition-all duration-300 group">
                <div className="card-content text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl mx-auto mb-4 animate-float"></div>
                  <h3 className="font-semibold text-gray-900">浮动动画</h3>
                  <p className="text-sm text-gray-600 mt-2">持续的上下浮动效果</p>
                </div>
              </div>
              
              <div className="card hover:shadow-glow transition-all duration-300 group">
                <div className="card-content text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl mx-auto mb-4 animate-pulse"></div>
                  <h3 className="font-semibold text-gray-900">脉冲动画</h3>
                  <p className="text-sm text-gray-600 mt-2">呼吸式的透明度变化</p>
                </div>
              </div>
              
              <div className="card hover:shadow-glow transition-all duration-300 group">
                <div className="card-content text-center">
                  <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl mx-auto mb-4 transform group-hover:rotate-12 transition-transform duration-300"></div>
                  <h3 className="font-semibold text-gray-900">旋转效果</h3>
                  <p className="text-sm text-gray-600 mt-2">悬浮时的旋转动画</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeDemo === 'glass' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 text-center">玻璃拟态效果</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="glass-card p-8 text-center">
                <Star className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">标准玻璃效果</h3>
                <p className="text-gray-600">半透明背景配合毛玻璃模糊效果</p>
              </div>
              
              <div className="bg-white/25 backdrop-blur-md rounded-2xl border border-white/20 shadow-glass p-8 text-center">
                <Heart className="w-12 h-12 text-pink-600 mx-auto mb-4" />
                <h3 className="text-xl font-bold text-gray-900 mb-2">增强玻璃效果</h3>
                <p className="text-gray-600">更强的模糊效果和边框高光</p>
              </div>
            </div>
          </div>
        )}

        {activeDemo === 'interactions' && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-900 text-center">交互体验</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <button className="btn-primary">主要按钮</button>
              <button className="btn-secondary">次要按钮</button>
              <button className="px-6 py-3 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 text-white font-medium rounded-2xl shadow-lg hover:shadow-glow transition-all duration-300 transform hover:scale-105">
                渐变按钮
              </button>
              
              <input 
                type="text" 
                placeholder="现代化输入框"
                className="modern-input"
              />
              
              <select className="modern-select">
                <option>现代化选择框</option>
                <option>选项 1</option>
                <option>选项 2</option>
              </select>
              
              <div className="card hover:shadow-glow transition-all duration-300 cursor-pointer transform hover:scale-105">
                <div className="card-content text-center">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg mx-auto mb-2"></div>
                  <p className="text-sm font-medium text-gray-900">悬浮卡片</p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default function DataScreenPage() {
  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between animate-slide-up">
        <div>
          <h1 className="text-3xl font-bold gradient-text">数据大屏</h1>
          <p className="mt-2 text-gray-600">实时数据可视化展示，支持多种图表和指标监控</p>
        </div>
        <button className="btn-primary">
          <span className="mr-2">+</span>
          新建大屏
        </button>
      </div>

      {/* 大屏列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[1, 2, 3, 4, 5, 6].map((item) => (
          <div key={item} className="card hover:shadow-medium transition-shadow cursor-pointer">
            <div className="aspect-video bg-gradient-to-br from-primary-500 to-primary-700 rounded-t-lg flex items-center justify-center">
              <span className="text-white font-medium">大屏预览 {item}</span>
            </div>
            <div className="card-content">
              <h3 className="font-semibold text-gray-900 mb-2">政务数据总览大屏 {item}</h3>
              <p className="text-sm text-gray-600 mb-4">展示政务服务各项关键指标和实时数据</p>
              <div className="flex items-center justify-between text-xs text-gray-500">
                <span>最后更新: 2024-01-15</span>
                <span>浏览: 1,234次</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

'use client'

import Link from 'next/link'
import { 
  ArrowLeft,
  FileText,
  Search,
  Filter,
  Download,
  Calendar,
  Clock,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle,
  Eye,
  RefreshCw,
  Settings,
  BarChart3,
  PieChart,
  TrendingUp,
  Server,
  User,
  Tag,
  Archive,
  Database,
  Zap
} from 'lucide-react'

export default function LogAnalysisPage() {
  // 日志统计
  const logStats = [
    { 
      label: '今日日志', 
      value: '2.3M', 
      change: '+15%',
      icon: FileText, 
      color: 'blue' 
    },
    { 
      label: '错误日志', 
      value: '1,245', 
      change: '-8%',
      icon: XCircle, 
      color: 'red' 
    },
    { 
      label: '警告日志', 
      value: '3,567', 
      change: '+3%',
      icon: AlertTriangle, 
      color: 'orange' 
    },
    { 
      label: '存储空间', 
      value: '156GB', 
      usage: '78%',
      icon: Database, 
      color: 'green' 
    }
  ]

  // 日志级别分布
  const logLevels = [
    { level: 'ERROR', count: 1245, color: 'red', percentage: 5.2 },
    { level: 'WARN', count: 3567, color: 'orange', percentage: 14.8 },
    { level: 'INFO', count: 15234, color: 'blue', percentage: 63.5 },
    { level: 'DEBUG', count: 3954, color: 'gray', percentage: 16.5 }
  ]

  // 日志来源统计
  const logSources = [
    { source: 'Web服务器', count: 8456, icon: Server, color: 'blue' },
    { source: '数据库服务器', count: 6234, icon: Database, color: 'green' },
    { source: '应用服务器', count: 5678, icon: Server, color: 'purple' },
    { source: '文件服务器', count: 3432, icon: Archive, color: 'orange' }
  ]

  // 最新日志列表
  const recentLogs = [
    {
      id: 1,
      timestamp: '2024-01-15 14:32:15',
      level: 'ERROR',
      source: 'Web服务器-01',
      service: 'nginx',
      message: 'Connection timeout to upstream server ************:3000',
      details: 'upstream timed out (110: Connection timed out) while connecting to upstream',
      user: 'system',
      ip: '************'
    },
    {
      id: 2,
      timestamp: '2024-01-15 14:31:45',
      level: 'WARN',
      source: '数据库服务器-01',
      service: 'mysql',
      message: 'Slow query detected: SELECT * FROM users WHERE status = 1',
      details: 'Query execution time: 5.234s, exceeds slow_query_log_time threshold',
      user: 'app_user',
      ip: '************'
    },
    {
      id: 3,
      timestamp: '2024-01-15 14:31:20',
      level: 'INFO',
      source: '应用服务器-01',
      service: 'app',
      message: 'User login successful',
      details: 'User ID: 12345, Session ID: sess_abc123def456',
      user: 'john.doe',
      ip: '**********'
    },
    {
      id: 4,
      timestamp: '2024-01-15 14:30:58',
      level: 'ERROR',
      source: '文件服务器-01',
      service: 'smb',
      message: 'Disk space critically low',
      details: 'Available space: 2.1GB (5%), threshold: 10%',
      user: 'system',
      ip: '************'
    },
    {
      id: 5,
      timestamp: '2024-01-15 14:30:30',
      level: 'DEBUG',
      source: 'Web服务器-02',
      service: 'apache',
      message: 'Cache miss for resource /api/users/profile',
      details: 'Cache key: user_profile_12345, TTL: 300s',
      user: 'cache_service',
      ip: '************'
    }
  ]

  // 热门搜索关键词
  const popularSearches = [
    { keyword: 'error', count: 1245 },
    { keyword: 'timeout', count: 892 },
    { keyword: 'connection', count: 756 },
    { keyword: 'database', count: 634 },
    { keyword: 'login', count: 523 },
    { keyword: 'slow query', count: 445 }
  ]

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'ERROR': return 'text-red-600 bg-red-100'
      case 'WARN': return 'text-orange-600 bg-orange-100'
      case 'INFO': return 'text-blue-600 bg-blue-100'
      case 'DEBUG': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getLevelIcon = (level: string) => {
    switch (level) {
      case 'ERROR': return <XCircle className="w-4 h-4" />
      case 'WARN': return <AlertTriangle className="w-4 h-4" />
      case 'INFO': return <Info className="w-4 h-4" />
      case 'DEBUG': return <Eye className="w-4 h-4" />
      default: return <FileText className="w-4 h-4" />
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/monitoring" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-3">
              <FileText className="w-8 h-8 text-emerald-600" />
              <span>日志分析</span>
            </h1>
            <p className="mt-1 text-gray-600">系统日志收集，日志分析和日志检索功能</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 bg-green-50 px-3 py-2 rounded-lg">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-green-700">实时收集中</span>
          </div>
          <button className="btn-secondary flex items-center space-x-2">
            <Download className="w-4 h-4" />
            <span>导出日志</span>
          </button>
          <button className="btn-secondary flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>日志设置</span>
          </button>
        </div>
      </div>

      {/* 日志统计 */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">日志统计</h2>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Clock className="w-4 h-4" />
            <span>最后更新: 2024-01-15 14:32</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {logStats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <div key={stat.label} className="card">
                <div className="card-content">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`w-12 h-12 rounded-2xl flex items-center justify-center ${
                      stat.color === 'blue' ? 'bg-blue-100' :
                      stat.color === 'red' ? 'bg-red-100' :
                      stat.color === 'orange' ? 'bg-orange-100' : 'bg-green-100'
                    }`}>
                      <Icon className={`w-6 h-6 ${
                        stat.color === 'blue' ? 'text-blue-600' :
                        stat.color === 'red' ? 'text-red-600' :
                        stat.color === 'orange' ? 'text-orange-600' : 'text-green-600'
                      }`} />
                    </div>
                    {stat.change && (
                      <div className="flex items-center space-x-1">
                        <TrendingUp className={`w-3 h-3 ${
                          stat.change.startsWith('-') ? 'text-green-500' : 'text-red-500'
                        }`} />
                        <span className={`text-xs font-medium ${
                          stat.change.startsWith('-') ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {stat.change}
                        </span>
                      </div>
                    )}
                  </div>
                  
                  <div className="text-sm font-medium text-gray-600 mb-1">{stat.label}</div>
                  <div className="text-2xl font-bold text-gray-900 mb-2">{stat.value}</div>
                  
                  {stat.usage && (
                    <div className="text-xs text-gray-500">使用率: {stat.usage}</div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </section>

      {/* 日志搜索 */}
      <section className="space-y-6">
        <h2 className="text-xl font-bold text-gray-900">日志搜索</h2>
        
        <div className="card">
          <div className="card-content">
            <div className="flex items-center space-x-4 mb-6">
              <div className="flex-1 relative">
                <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索日志内容、服务名称、IP地址..."
                  className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
              <div className="flex items-center space-x-2">
                <select className="px-3 py-3 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option>所有级别</option>
                  <option>ERROR</option>
                  <option>WARN</option>
                  <option>INFO</option>
                  <option>DEBUG</option>
                </select>
                <select className="px-3 py-3 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500">
                  <option>所有来源</option>
                  <option>Web服务器</option>
                  <option>数据库服务器</option>
                  <option>应用服务器</option>
                  <option>文件服务器</option>
                </select>
                <button className="btn-primary px-6 py-3">搜索</button>
              </div>
            </div>

            {/* 热门搜索 */}
            <div className="border-t border-gray-100 pt-4">
              <h4 className="text-sm font-medium text-gray-700 mb-3">热门搜索</h4>
              <div className="flex flex-wrap gap-2">
                {popularSearches.map((search, index) => (
                  <button
                    key={index}
                    className="px-3 py-1 text-xs bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-full transition-colors"
                  >
                    {search.keyword} ({search.count})
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 日志分析图表 */}
      <section className="space-y-6">
        <h2 className="text-xl font-bold text-gray-900">日志分析</h2>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 日志级别分布 */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                <PieChart className="w-5 h-5 text-purple-600" />
                <span>日志级别分布</span>
              </h3>
            </div>
            <div className="card-content">
              <div className="space-y-4">
                {logLevels.map((level) => (
                  <div key={level.level} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className={`px-2 py-1 text-xs font-medium rounded ${getLevelColor(level.level)}`}>
                        {level.level}
                      </span>
                      <span className="text-sm text-gray-600">{level.count.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-20 bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            level.color === 'red' ? 'bg-red-500' :
                            level.color === 'orange' ? 'bg-orange-500' :
                            level.color === 'blue' ? 'bg-blue-500' : 'bg-gray-500'
                          }`}
                          style={{ width: `${level.percentage}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium text-gray-900">{level.percentage}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 日志来源统计 */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                <BarChart3 className="w-5 h-5 text-blue-600" />
                <span>日志来源统计</span>
              </h3>
            </div>
            <div className="card-content">
              <div className="space-y-4">
                {logSources.map((source) => {
                  const Icon = source.icon
                  return (
                    <div key={source.source} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                          source.color === 'blue' ? 'bg-blue-100' :
                          source.color === 'green' ? 'bg-green-100' :
                          source.color === 'purple' ? 'bg-purple-100' : 'bg-orange-100'
                        }`}>
                          <Icon className={`w-4 h-4 ${
                            source.color === 'blue' ? 'text-blue-600' :
                            source.color === 'green' ? 'text-green-600' :
                            source.color === 'purple' ? 'text-purple-600' : 'text-orange-600'
                          }`} />
                        </div>
                        <span className="text-sm font-medium text-gray-900">{source.source}</span>
                      </div>
                      <span className="text-lg font-bold text-gray-900">{source.count.toLocaleString()}</span>
                    </div>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 最新日志 */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">最新日志</h2>
          <div className="flex items-center space-x-4">
            <button className="btn-secondary flex items-center space-x-2">
              <RefreshCw className="w-4 h-4" />
              <span>刷新</span>
            </button>
            <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
              查看全部 →
            </button>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="space-y-4">
              {recentLogs.map((log) => (
                <div key={log.id} className="border border-gray-100 rounded-xl p-6 hover:bg-gray-50/50 transition-colors">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-start space-x-4">
                      <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${getLevelColor(log.level)}`}>
                        {getLevelIcon(log.level)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <span className={`px-2 py-1 text-xs font-medium rounded ${getLevelColor(log.level)}`}>
                            {log.level}
                          </span>
                          <span className="text-sm font-medium text-gray-900">{log.source}</span>
                          <span className="text-sm text-gray-500">{log.service}</span>
                        </div>
                        <h4 className="font-medium text-gray-900 mb-2">{log.message}</h4>
                        <p className="text-sm text-gray-600 mb-3">{log.details}</p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <span className="flex items-center space-x-1">
                            <Clock className="w-3 h-3" />
                            <span>{log.timestamp}</span>
                          </span>
                          <span className="flex items-center space-x-1">
                            <User className="w-3 h-3" />
                            <span>{log.user}</span>
                          </span>
                          <span>{log.ip}</span>
                        </div>
                      </div>
                    </div>
                    
                    <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                      <Eye className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

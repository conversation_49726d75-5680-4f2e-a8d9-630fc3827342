'use client'

import { Folder, Database, FileText, Globe, Search, Filter, Grid, List } from 'lucide-react'
import { useState } from 'react'

export default function ResourceDirectoryPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [searchTerm, setSearchTerm] = useState('')

  const categories = [
    {
      id: 'data',
      name: '数据目录',
      icon: Database,
      count: 156,
      description: '结构化数据资源目录',
      color: 'indigo'
    },
    {
      id: 'files',
      name: '文件目录',
      icon: FileText,
      count: 89,
      description: '文档和文件资源目录',
      color: 'blue'
    },
    {
      id: 'apis',
      name: '接口目录',
      icon: Globe,
      count: 34,
      description: 'API和服务接口目录',
      color: 'green'
    }
  ]

  const resources = [
    {
      id: 1,
      name: '人口基础数据库',
      category: 'data',
      type: '数据库',
      department: '公安局',
      tags: ['基础数据', '人口', '实时'],
      lastUpdated: '2024-01-15'
    },
    {
      id: 2,
      name: '企业注册信息',
      category: 'data',
      type: '数据集',
      department: '市场监管局',
      tags: ['企业', '注册', '公开'],
      lastUpdated: '2024-01-14'
    },
    {
      id: 3,
      name: '政策文件库',
      category: 'files',
      type: '文档库',
      department: '政府办',
      tags: ['政策', '文档', '公开'],
      lastUpdated: '2024-01-13'
    },
    {
      id: 4,
      name: '数据查询API',
      category: 'apis',
      type: 'REST API',
      department: '数据局',
      tags: ['API', '查询', '实时'],
      lastUpdated: '2024-01-12'
    }
  ]

  const filteredResources = resources.filter(resource =>
    resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    resource.department.toLowerCase().includes(searchTerm.toLowerCase()) ||
    resource.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
  )

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">资源目录</h1>
        <p className="text-xl text-gray-600">浏览和搜索所有可用的数据资源</p>
      </div>

      {/* 分类概览 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {categories.map((category) => {
          const Icon = category.icon
          return (
            <div
              key={category.id}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 cursor-pointer"
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-br from-${category.color}-500 to-${category.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <span className="text-2xl font-bold text-gray-900">{category.count}</span>
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">{category.name}</h3>
              <p className="text-sm text-gray-600">{category.description}</p>
            </div>
          )
        })}
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="搜索资源..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              />
            </div>
            <button className="flex items-center space-x-2 px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Filter className="w-5 h-5" />
              <span>筛选</span>
            </button>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setViewMode('grid')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'grid' ? 'bg-indigo-100 text-indigo-600' : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              <Grid className="w-5 h-5" />
            </button>
            <button
              onClick={() => setViewMode('list')}
              className={`p-2 rounded-lg transition-colors ${
                viewMode === 'list' ? 'bg-indigo-100 text-indigo-600' : 'text-gray-400 hover:text-gray-600'
              }`}
            >
              <List className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>

      {/* 资源列表 */}
      <div className={viewMode === 'grid' ? 'grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6' : 'space-y-4'}>
        {filteredResources.map((resource) => (
          <div
            key={resource.id}
            className={`bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 ${
              viewMode === 'grid' ? 'p-6' : 'p-4 flex items-center space-x-4'
            }`}
          >
            {viewMode === 'grid' ? (
              <>
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-bold text-gray-900 mb-2">{resource.name}</h3>
                    <div className="flex items-center space-x-2 text-sm text-gray-600 mb-2">
                      <span className="bg-gray-100 px-2 py-1 rounded-full">{resource.type}</span>
                      <span>{resource.department}</span>
                    </div>
                  </div>
                  <Folder className="w-8 h-8 text-indigo-500" />
                </div>
                <div className="flex flex-wrap gap-2 mb-4">
                  {resource.tags.map((tag) => (
                    <span key={tag} className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
                      {tag}
                    </span>
                  ))}
                </div>
                <div className="text-sm text-gray-500">
                  最后更新: {resource.lastUpdated}
                </div>
              </>
            ) : (
              <>
                <Folder className="w-8 h-8 text-indigo-500 flex-shrink-0" />
                <div className="flex-1">
                  <h3 className="text-lg font-bold text-gray-900">{resource.name}</h3>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <span>{resource.type}</span>
                    <span>{resource.department}</span>
                    <span>更新: {resource.lastUpdated}</span>
                  </div>
                </div>
                <div className="flex flex-wrap gap-1">
                  {resource.tags.map((tag) => (
                    <span key={tag} className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
                      {tag}
                    </span>
                  ))}
                </div>
              </>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

import { DashboardOverview } from '@/components/dashboard/DashboardOverview'
import { BusinessModules } from '@/components/dashboard/BusinessModules'
import { SystemStatus } from '@/components/dashboard/SystemStatus'
import { QuickActions } from '@/components/dashboard/QuickActions'

export default function DashboardPage() {
  return (
    <div className="space-y-10">
      {/* 页面标题 */}
      <div className="text-center mb-12 animate-fade-in">
        <div className="inline-flex items-center space-x-4 mb-6">
          <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center shadow-lg">
            <div className="w-6 h-6 bg-white rounded-lg"></div>
          </div>
          <div>
            <h1 className="text-4xl font-bold gradient-text">云宇政数平台</h1>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full mt-2 mx-auto"></div>
          </div>
        </div>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed">
          统一的政数局数据管理与监控平台，提供全方位的数据治理和智能分析服务
        </p>
        <div className="flex items-center justify-center space-x-6 mt-6 text-sm text-gray-500">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span>系统运行正常</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span>数据同步中</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse"></div>
            <span>AI分析就绪</span>
          </div>
        </div>
      </div>

      {/* 概览统计 */}
      <div className="animate-slide-up">
        <DashboardOverview />
      </div>

      {/* 业务模块入口 */}
      <div className="animate-slide-up" style={{ animationDelay: '200ms' }}>
        <BusinessModules />
      </div>

      {/* 系统状态和快捷操作 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 animate-slide-up" style={{ animationDelay: '400ms' }}>
        <div className="lg:col-span-2">
          <SystemStatus />
        </div>
        <div>
          <QuickActions />
        </div>
      </div>
    </div>
  )
}

'use client'

import Link from 'next/link'
import { 
  FileText, 
  BarChart3, 
  PieChart, 
  TrendingUp, 
  Download,
  Calendar,
  Filter,
  Plus,
  ArrowRight,
  Clock,
  Users,
  Eye
} from 'lucide-react'

export default function ReportsSystemPage() {
  const reports = [
    {
      id: 1,
      name: '月度政务数据汇总',
      description: '全市政务数据月度统计分析报告',
      type: '定期报表',
      status: 'completed',
      lastGenerated: '2024-01-15',
      downloads: 156,
      category: '综合统计'
    },
    {
      id: 2,
      name: '人口流动分析',
      description: '人口数据变化趋势分析',
      type: '专项分析',
      status: 'generating',
      lastGenerated: '2024-01-14',
      downloads: 89,
      category: '人口统计'
    },
    {
      id: 3,
      name: '经济指标监控',
      description: '经济运行关键指标监控报告',
      type: '实时报表',
      status: 'completed',
      lastGenerated: '2024-01-15',
      downloads: 234,
      category: '经济分析'
    },
    {
      id: 4,
      name: '环境质量评估',
      description: '环境数据质量评估报告',
      type: '质量报告',
      status: 'scheduled',
      lastGenerated: '2024-01-10',
      downloads: 67,
      category: '环境监测'
    }
  ]

  const quickStats = [
    { label: '总报表数', value: '156', trend: '+12', icon: FileText, color: 'green' },
    { label: '本月生成', value: '24', trend: '+8', icon: Calendar, color: 'blue' },
    { label: '总下载量', value: '2.4K', trend: '+15%', icon: Download, color: 'purple' },
    { label: '活跃用户', value: '89', trend: '+5', icon: Users, color: 'orange' }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-700'
      case 'generating': return 'bg-blue-100 text-blue-700'
      case 'scheduled': return 'bg-yellow-100 text-yellow-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed': return '已完成'
      case 'generating': return '生成中'
      case 'scheduled': return '已计划'
      default: return '未知'
    }
  }

  return (
    <div className="max-w-7xl mx-auto px-6 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">报表分析中心</h1>
        <p className="text-xl text-gray-600">创建、管理和分析您的数据报表</p>
      </div>

      {/* 快速统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {quickStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-sm text-green-600 font-medium">{stat.trend}</p>
                </div>
                <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 操作按钮 */}
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-4">
          <button className="bg-green-600 text-white px-6 py-3 rounded-xl hover:bg-green-700 transition-colors flex items-center space-x-2 font-medium">
            <Plus className="w-5 h-5" />
            <span>创建报表</span>
          </button>
          <button className="bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium">
            <Filter className="w-5 h-5" />
            <span>筛选</span>
          </button>
        </div>
      </div>

      {/* 报表列表 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {reports.map((report, index) => (
          <div
            key={report.id}
            className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 group"
          >
            <div className="p-6">
              {/* 头部 */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <h3 className="text-lg font-bold text-gray-900">{report.name}</h3>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(report.status)}`}>
                      {getStatusText(report.status)}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-2">{report.description}</p>
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">{report.category}</span>
                </div>
                <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center flex-shrink-0">
                  <FileText className="w-6 h-6 text-white" />
                </div>
              </div>

              {/* 统计信息 */}
              <div className="grid grid-cols-3 gap-4 mb-4 p-4 bg-gray-50/50 rounded-xl">
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <Clock className="w-4 h-4 text-gray-500 mr-1" />
                  </div>
                  <p className="text-xs text-gray-600">最后生成</p>
                  <p className="text-sm font-medium text-gray-900">{report.lastGenerated}</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <Download className="w-4 h-4 text-gray-500 mr-1" />
                  </div>
                  <p className="text-xs text-gray-600">下载次数</p>
                  <p className="text-sm font-medium text-gray-900">{report.downloads}</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <BarChart3 className="w-4 h-4 text-gray-500 mr-1" />
                  </div>
                  <p className="text-xs text-gray-600">报表类型</p>
                  <p className="text-sm font-medium text-gray-900">{report.type}</p>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <button className="text-green-600 hover:text-green-700 font-medium text-sm transition-colors">
                    预览
                  </button>
                  <button className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors">
                    下载
                  </button>
                </div>
                <Link
                  href={`/reports/view/${report.id}`}
                  className="text-green-600 hover:text-green-700 font-medium flex items-center space-x-1 transition-colors text-sm"
                >
                  <span>查看详情</span>
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

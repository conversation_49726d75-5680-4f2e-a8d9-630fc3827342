# 🌟 云宇政数平台 - 现代化版本

> 统一的政数局数据管理平台，采用最新现代化设计理念和技术栈构建

✨ **全新现代化界面设计** | 🎨 **玻璃拟态风格** | 🚀 **流畅动画体验** | 📱 **完美响应式布局**

提供大屏展示、报表分析、数据采集、数据汇聚、数据清洗治理、数据资源池管理、设备监控等全方位的数据管理服务。

## 🚀 技术栈

- **前端框架**: React 18 + Next.js 14
- **样式方案**: TailwindCSS
- **包管理器**: pnpm
- **架构模式**: Monorepo
- **开发语言**: TypeScript
- **图标库**: Lucide React

## 📁 项目结构

```
web/
├── apps/
│   └── main/                 # 主应用
│       ├── src/
│       │   ├── app/         # Next.js App Router
│       │   │   ├── dashboard/    # 仪表板页面
│       │   │   │   ├── page.tsx           # 总览页面
│       │   │   │   ├── screen/            # 数据大屏
│       │   │   │   ├── reports/           # 报表分析
│       │   │   │   ├── collection/        # 数据采集
│       │   │   │   ├── aggregation/       # 数据汇聚
│       │   │   │   ├── governance/        # 数据治理
│       │   │   │   ├── resources/         # 资源池管理
│       │   │   │   ├── monitoring/        # 设备监控
│       │   │   │   ├── network/           # 网络管理
│       │   │   │   └── settings/          # 系统设置
│       │   │   ├── layout.tsx             # 根布局
│       │   │   └── globals.css            # 全局样式
│       │   └── components/               # 组件
│       │       ├── layout/              # 布局组件
│       │       └── dashboard/           # 仪表板组件
│       ├── package.json
│       ├── next.config.js
│       ├── tailwind.config.js
│       └── tsconfig.json
├── packages/
│   ├── ui/                   # 共享UI组件库
│   │   ├── src/
│   │   │   ├── components/  # UI组件
│   │   │   ├── utils/       # 工具函数
│   │   │   └── index.ts     # 导出文件
│   │   └── package.json
│   └── utils/               # 共享工具库
│       ├── src/
│       │   ├── format.ts    # 格式化工具
│       │   ├── date.ts      # 日期工具
│       │   └── index.ts     # 导出文件
│       └── package.json
├── package.json             # 根配置
├── pnpm-workspace.yaml      # pnpm工作区配置
└── README.md
```

## 🎯 功能模块

### 1. 总览仪表板
- 系统关键指标展示
- 业务模块快速入口
- 系统状态监控
- 快捷操作面板

### 2. 数据大屏
- 实时数据可视化展示
- 多种图表和指标监控
- 大屏模板管理

### 3. 报表分析
- 多维度数据分析
- 自定义报表生成
- 报表分类管理
- 导出功能

### 4. 数据采集
- 多源数据接入
- 实时和批量数据导入
- 数据源状态监控
- 采集任务管理

### 5. 数据汇聚
- 跨系统数据整合
- 数据标准化处理
- 汇聚任务调度
- 进度监控

### 6. 数据治理
- 数据质量评估
- 数据清洗规则
- 重复数据检测
- 数据标准化

### 7. 资源池管理
- 数据资源目录
- 权限控制管理
- 存储容量监控
- API接口管理

### 8. 设备监控
- 服务器性能监控
- 实时状态展示
- 告警管理
- 设备管理

### 9. 网络管理
- 多物理网络管理
- 网络拓扑展示
- 流量监控
- 带宽利用率

### 10. 系统设置
- 基础配置管理
- 用户权限设置
- 性能参数调优
- 系统维护

## 🛠️ 开发指南

### 环境要求

- Node.js >= 18.0.0
- pnpm >= 8.0.0

### 安装依赖

```bash
cd web
pnpm install
```

### 启动开发服务器

```bash
# 启动主应用
pnpm dev

# 或启动所有应用
pnpm dev:all
```

### 构建项目

```bash
pnpm build
```

### 代码检查

```bash
pnpm lint
```

### 类型检查

```bash
pnpm type-check
```

## 🎨 现代化设计特色

### 🌈 全新视觉体验
- **玻璃拟态设计**: 半透明背景 + 毛玻璃效果
- **渐变色彩方案**: 多层次渐变色彩搭配
- **动态光效**: 悬浮发光效果和动画交互
- **现代化圆角**: 统一的圆角设计语言

### ✨ 交互动画
- **流畅过渡**: 300ms+ 缓动动画
- **悬浮效果**: 卡片悬浮和缩放效果
- **加载动画**: 渐入、滑入等入场动画
- **状态反馈**: 实时的视觉状态反馈

### 🎯 布局优化
- **响应式网格**: 自适应多设备布局
- **视觉层次**: 清晰的信息架构
- **空间利用**: 充分利用屏幕空间
- **用户体验**: 直观的操作流程

### 🔧 组件升级
- **现代化按钮**: 渐变背景 + 发光效果
- **智能卡片**: 玻璃拟态 + 悬浮动画
- **优雅表单**: 现代化输入框设计
- **统一图标**: Lucide React 图标库

## 📝 开发规范

### 文件命名
- 组件文件使用 PascalCase
- 工具函数使用 camelCase
- 页面文件使用 kebab-case

### 代码规范
- 使用 TypeScript 进行类型检查
- 遵循 ESLint 代码规范
- 使用 Prettier 格式化代码
- 组件props必须定义类型

### Git 提交规范
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 样式调整
- refactor: 代码重构

## 🚀 部署说明

### 生产环境构建
```bash
pnpm build
pnpm start
```

### Docker 部署
```dockerfile
# 待补充 Dockerfile 配置
```

## 📞 技术支持

如有问题或建议，请联系开发团队。

---

**云宇政数平台** - 让数据管理更简单、更高效！

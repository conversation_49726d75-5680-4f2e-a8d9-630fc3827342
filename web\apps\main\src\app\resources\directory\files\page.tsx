'use client'

import { FileText, File, Image, Video, Archive, Download, Eye, Calendar, User, HardDrive } from 'lucide-react'

export default function FilesDirectoryPage() {
  const fileCategories = [
    {
      id: 'documents',
      name: '文档资料',
      icon: FileText,
      count: 1234,
      size: '45GB',
      color: 'blue'
    },
    {
      id: 'images',
      name: '图片资源',
      icon: Image,
      count: 567,
      size: '12GB',
      color: 'green'
    },
    {
      id: 'videos',
      name: '视频资料',
      icon: Video,
      count: 89,
      size: '234GB',
      color: 'purple'
    },
    {
      id: 'archives',
      name: '压缩文件',
      icon: Archive,
      count: 156,
      size: '67GB',
      color: 'orange'
    }
  ]

  const files = [
    {
      id: 1,
      name: '2024年政府工作报告.pdf',
      type: 'PDF文档',
      size: '2.4MB',
      department: '政府办',
      uploadDate: '2024-01-15',
      downloads: 156,
      category: 'documents',
      description: '2024年度政府工作报告完整版'
    },
    {
      id: 2,
      name: '城市规划图集.zip',
      type: '压缩文件',
      size: '156MB',
      department: '规划局',
      uploadDate: '2024-01-14',
      downloads: 89,
      category: 'archives',
      description: '城市总体规划图纸集合'
    },
    {
      id: 3,
      name: '环境监测数据报告.xlsx',
      type: 'Excel表格',
      size: '5.6MB',
      department: '环保局',
      uploadDate: '2024-01-13',
      downloads: 234,
      category: 'documents',
      description: '2023年度环境监测数据统计报告'
    },
    {
      id: 4,
      name: '城市宣传片.mp4',
      type: '视频文件',
      size: '234MB',
      department: '宣传部',
      uploadDate: '2024-01-12',
      downloads: 67,
      category: 'videos',
      description: '城市形象宣传视频'
    },
    {
      id: 5,
      name: '基础设施建设图片.jpg',
      type: '图片文件',
      size: '8.9MB',
      department: '建设局',
      uploadDate: '2024-01-11',
      downloads: 123,
      category: 'images',
      description: '基础设施建设现场照片'
    }
  ]

  const getFileIcon = (category: string) => {
    switch (category) {
      case 'documents': return <FileText className="w-6 h-6 text-blue-500" />
      case 'images': return <Image className="w-6 h-6 text-green-500" />
      case 'videos': return <Video className="w-6 h-6 text-purple-500" />
      case 'archives': return <Archive className="w-6 h-6 text-orange-500" />
      default: return <File className="w-6 h-6 text-gray-500" />
    }
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">文件目录</h1>
        <p className="text-xl text-gray-600">浏览和管理文档、图片、视频等文件资源</p>
      </div>

      {/* 文件分类统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {fileCategories.map((category) => {
          const Icon = category.icon
          return (
            <div
              key={category.id}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 cursor-pointer"
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-br from-${category.color}-500 to-${category.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-gray-900">{category.count}</p>
                  <p className="text-sm text-gray-600">{category.size}</p>
                </div>
              </div>
              <h3 className="text-lg font-bold text-gray-900">{category.name}</h3>
            </div>
          )
        })}
      </div>

      {/* 存储使用情况 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <h3 className="text-lg font-bold text-gray-900 mb-4">存储使用情况</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-xl flex items-center justify-center mx-auto mb-3">
              <HardDrive className="w-8 h-8 text-white" />
            </div>
            <p className="text-2xl font-bold text-gray-900">358GB</p>
            <p className="text-sm text-gray-600">总存储空间</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mx-auto mb-3">
              <HardDrive className="w-8 h-8 text-white" />
            </div>
            <p className="text-2xl font-bold text-gray-900">289GB</p>
            <p className="text-sm text-gray-600">已使用空间</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center mx-auto mb-3">
              <HardDrive className="w-8 h-8 text-white" />
            </div>
            <p className="text-2xl font-bold text-gray-900">69GB</p>
            <p className="text-sm text-gray-600">剩余空间</p>
          </div>
        </div>
        <div className="mt-6">
          <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
            <span>存储使用率</span>
            <span>80.7%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div className="bg-gradient-to-r from-indigo-500 to-blue-500 h-2 rounded-full" style={{ width: '80.7%' }}></div>
          </div>
        </div>
      </div>

      {/* 文件列表 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <h3 className="text-lg font-bold text-gray-900">最近文件</h3>
        </div>
        <div className="divide-y divide-gray-100">
          {files.map((file) => (
            <div
              key={file.id}
              className="p-6 hover:bg-gray-50/50 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 flex-1">
                  <div className="flex-shrink-0">
                    {getFileIcon(file.category)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="text-lg font-medium text-gray-900 truncate">{file.name}</h4>
                    <p className="text-sm text-gray-600 mb-1">{file.description}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span className="flex items-center space-x-1">
                        <File className="w-4 h-4" />
                        <span>{file.type}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <HardDrive className="w-4 h-4" />
                        <span>{file.size}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <User className="w-4 h-4" />
                        <span>{file.department}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Calendar className="w-4 h-4" />
                        <span>{file.uploadDate}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Download className="w-4 h-4" />
                        <span>{file.downloads} 次下载</span>
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="text-indigo-600 hover:text-indigo-700 font-medium text-sm transition-colors flex items-center space-x-1">
                    <Eye className="w-4 h-4" />
                    <span>预览</span>
                  </button>
                  <button className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors flex items-center space-x-1">
                    <Download className="w-4 h-4" />
                    <span>下载</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

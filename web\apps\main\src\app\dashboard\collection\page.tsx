export default function DataCollectionPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">数据采集</h1>
          <p className="mt-1 text-gray-600">多源数据采集接入管理</p>
        </div>
        <button className="btn-primary">
          新增数据源
        </button>
      </div>

      {/* 采集统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[
          { label: '数据源总数', value: '24', unit: '个' },
          { label: '今日采集量', value: '1.2', unit: 'GB' },
          { label: '采集成功率', value: '98.5', unit: '%' },
          { label: '实时任务', value: '12', unit: '个' },
        ].map((stat) => (
          <div key={stat.label} className="card">
            <div className="card-content text-center">
              <div className="text-2xl font-bold text-primary-600">
                {stat.value}
                <span className="text-sm text-gray-500 ml-1">{stat.unit}</span>
              </div>
              <div className="text-sm text-gray-600 mt-1">{stat.label}</div>
            </div>
          </div>
        ))}
      </div>

      {/* 数据源列表 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">数据源管理</h3>
        </div>
        <div className="card-content">
          <div className="space-y-4">
            {[
              { name: '政务服务数据库', type: 'MySQL', status: '运行中', lastSync: '2分钟前' },
              { name: '人口信息系统', type: 'Oracle', status: '运行中', lastSync: '5分钟前' },
              { name: '经济统计API', type: 'REST API', status: '异常', lastSync: '1小时前' },
              { name: '社保数据文件', type: 'CSV文件', status: '运行中', lastSync: '10分钟前' },
            ].map((source, index) => (
              <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className={`w-3 h-3 rounded-full ${
                    source.status === '运行中' ? 'bg-success-500' : 'bg-error-500'
                  }`}></div>
                  <div>
                    <div className="font-medium text-gray-900">{source.name}</div>
                    <div className="text-sm text-gray-500">{source.type}</div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-sm text-gray-600">
                    最后同步: {source.lastSync}
                  </div>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    source.status === '运行中' 
                      ? 'bg-success-100 text-success-700' 
                      : 'bg-error-100 text-error-700'
                  }`}>
                    {source.status}
                  </span>
                  <button className="text-primary-600 hover:text-primary-700 text-sm">
                    配置
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

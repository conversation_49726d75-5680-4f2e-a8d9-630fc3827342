export default function DataAggregationPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">数据汇聚</h1>
          <p className="mt-1 text-gray-600">跨系统数据整合汇聚管理</p>
        </div>
        <button className="btn-primary">
          新建汇聚任务
        </button>
      </div>

      {/* 汇聚任务状态 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[
          { label: '汇聚任务总数', value: '8', color: 'primary' },
          { label: '运行中任务', value: '6', color: 'success' },
          { label: '待处理任务', value: '1', color: 'warning' },
          { label: '异常任务', value: '1', color: 'error' },
        ].map((stat) => (
          <div key={stat.label} className="card">
            <div className="card-content text-center">
              <div className={`text-2xl font-bold ${
                stat.color === 'primary' ? 'text-primary-600' :
                stat.color === 'success' ? 'text-success-600' :
                stat.color === 'warning' ? 'text-warning-600' :
                'text-error-600'
              }`}>
                {stat.value}
              </div>
              <div className="text-sm text-gray-600 mt-1">{stat.label}</div>
            </div>
          </div>
        ))}
      </div>

      {/* 汇聚任务列表 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">汇聚任务</h3>
        </div>
        <div className="card-content">
          <div className="space-y-4">
            {[
              { name: '政务数据统一汇聚', sources: ['政务服务DB', '人口信息系统'], target: '政务数据仓库', status: '运行中', progress: 85 },
              { name: '经济数据整合', sources: ['统计局API', '税务系统'], target: '经济分析库', status: '运行中', progress: 92 },
              { name: '社保数据汇总', sources: ['社保系统', '医保系统'], target: '民生数据库', status: '待处理', progress: 0 },
              { name: '教育数据集成', sources: ['教育局DB', '学校系统'], target: '教育数据仓库', status: '异常', progress: 45 },
            ].map((task, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="font-medium text-gray-900">{task.name}</h4>
                    <div className="text-sm text-gray-600 mt-1">
                      数据源: {task.sources.join(', ')} → 目标: {task.target}
                    </div>
                  </div>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    task.status === '运行中' ? 'bg-success-100 text-success-700' :
                    task.status === '待处理' ? 'bg-warning-100 text-warning-700' :
                    'bg-error-100 text-error-700'
                  }`}>
                    {task.status}
                  </span>
                </div>
                
                {task.status === '运行中' && (
                  <div className="mb-3">
                    <div className="flex items-center justify-between text-sm text-gray-600 mb-1">
                      <span>进度</span>
                      <span>{task.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${task.progress}%` }}
                      ></div>
                    </div>
                  </div>
                )}
                
                <div className="flex space-x-2">
                  <button className="text-primary-600 hover:text-primary-700 text-sm">查看详情</button>
                  <button className="text-gray-600 hover:text-gray-700 text-sm">编辑配置</button>
                  {task.status === '异常' && (
                    <button className="text-error-600 hover:text-error-700 text-sm">重新运行</button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

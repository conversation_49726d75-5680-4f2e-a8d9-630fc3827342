import React from 'react'

interface ModernCardProps {
  children: React.ReactNode
  className?: string
  hover?: boolean
  glass?: boolean
  gradient?: string
  padding?: 'sm' | 'md' | 'lg'
}

export function ModernCard({
  children,
  className = '',
  hover = true,
  glass = false,
  gradient,
  padding = 'md',
}: ModernCardProps) {
  const baseClasses = 'rounded-2xl border transition-all duration-300'
  
  const hoverClasses = hover ? 'hover:shadow-glow transform hover:scale-[1.02]' : ''
  
  const backgroundClasses = glass 
    ? 'bg-white/25 backdrop-blur-md border-white/20 shadow-glass'
    : 'bg-white/80 backdrop-blur-sm border-white/20 shadow-glass'
  
  const paddingClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8',
  }
  
  const classes = `${baseClasses} ${backgroundClasses} ${hoverClasses} ${paddingClasses[padding]} ${className}`
  
  return (
    <div className={classes}>
      {gradient && (
        <div className={`absolute inset-0 bg-gradient-to-br ${gradient} opacity-5 rounded-2xl`}></div>
      )}
      <div className="relative z-10">
        {children}
      </div>
    </div>
  )
}

export function ModernCardHeader({
  children,
  className = '',
}: {
  children: React.ReactNode
  className?: string
}) {
  return (
    <div className={`pb-4 border-b border-gray-100/50 mb-6 ${className}`}>
      {children}
    </div>
  )
}

export function ModernCardContent({
  children,
  className = '',
}: {
  children: React.ReactNode
  className?: string
}) {
  return (
    <div className={className}>
      {children}
    </div>
  )
}

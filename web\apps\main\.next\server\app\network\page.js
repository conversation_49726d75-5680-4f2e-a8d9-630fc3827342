/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/network/page";
exports.ids = ["app/network/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnetwork%2Fpage&page=%2Fnetwork%2Fpage&appPaths=%2Fnetwork%2Fpage&pagePath=private-next-app-dir%2Fnetwork%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnetwork%2Fpage&page=%2Fnetwork%2Fpage&appPaths=%2Fnetwork%2Fpage&pagePath=private-next-app-dir%2Fnetwork%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?3032\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'network',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/network/page.tsx */ \"(rsc)/./src/app/network/page.tsx\")), \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/network/layout.tsx */ \"(rsc)/./src/app/network/layout.tsx\")), \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/network/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/network/page\",\n        pathname: \"/network\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnetwork%2Fpage&page=%2Fnetwork%2Fpage&appPaths=%2Fnetwork%2Fpage&pagePath=private-next-app-dir%2Fnetwork%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cnetwork%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cnetwork%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/network/layout.tsx */ \"(ssr)/./src/app/network/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMi4zMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q1dvcmtzcGFjZSU1QyU1Q3l5LXpzJTVDJTVDd2ViJTVDJTVDYXBwcyU1QyU1Q21haW4lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNuZXR3b3JrJTVDJTVDbGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsb0tBQXlHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHl1bnl1L21haW4vP2QyNDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxXb3Jrc3BhY2VcXFxceXktenNcXFxcd2ViXFxcXGFwcHNcXFxcbWFpblxcXFxzcmNcXFxcYXBwXFxcXG5ldHdvcmtcXFxcbGF5b3V0LnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cnetwork%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cnetwork%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cnetwork%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/network/page.tsx */ \"(ssr)/./src/app/network/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMi4zMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q1dvcmtzcGFjZSU1QyU1Q3l5LXpzJTVDJTVDd2ViJTVDJTVDYXBwcyU1QyU1Q21haW4lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNuZXR3b3JrJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLGdLQUF1RyIsInNvdXJjZXMiOlsid2VicGFjazovL0B5dW55dS9tYWluLz8yZDU4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcV29ya3NwYWNlXFxcXHl5LXpzXFxcXHdlYlxcXFxhcHBzXFxcXG1haW5cXFxcc3JjXFxcXGFwcFxcXFxuZXR3b3JrXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cnetwork%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMi4zMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q1dvcmtzcGFjZSU1QyU1Q3l5LXpzJTVDJTVDd2ViJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDLnBucG0lNUMlNUNuZXh0JTQwMTQuMi4zMF9yZWFjdC1kb20lNDAxOC4zLjFfcmVhY3QlNDAxOC4zLjFfX3JlYWN0JTQwMTguMy4xJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2Rpc3QlNUMlNUNjbGllbnQlNUMlNUNjb21wb25lbnRzJTVDJTVDYXBwLXJvdXRlci5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDV29ya3NwYWNlJTVDJTVDeXktenMlNUMlNUN3ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNC4yLjMwX3JlYWN0LWRvbSU0MDE4LjMuMV9yZWFjdCU0MDE4LjMuMV9fcmVhY3QlNDAxOC4zLjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNjbGllbnQtcGFnZS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDV29ya3NwYWNlJTVDJTVDeXktenMlNUMlNUN3ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNC4yLjMwX3JlYWN0LWRvbSU0MDE4LjMuMV9yZWFjdCU0MDE4LjMuMV9fcmVhY3QlNDAxOC4zLjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNlcnJvci1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDV29ya3NwYWNlJTVDJTVDeXktenMlNUMlNUN3ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNC4yLjMwX3JlYWN0LWRvbSU0MDE4LjMuMV9yZWFjdCU0MDE4LjMuMV9fcmVhY3QlNDAxOC4zLjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNsYXlvdXQtcm91dGVyLmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNXb3Jrc3BhY2UlNUMlNUN5eS16cyU1QyU1Q3dlYiU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Qy5wbnBtJTVDJTVDbmV4dCU0MDE0LjIuMzBfcmVhY3QtZG9tJTQwMTguMy4xX3JlYWN0JTQwMTguMy4xX19yZWFjdCU0MDE4LjMuMSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNkaXN0JTVDJTVDY2xpZW50JTVDJTVDY29tcG9uZW50cyU1QyU1Q25vdC1mb3VuZC1ib3VuZGFyeS5qcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJFJTNBJTVDJTVDV29ya3NwYWNlJTVDJTVDeXktenMlNUMlNUN3ZWIlNUMlNUNub2RlX21vZHVsZXMlNUMlNUMucG5wbSU1QyU1Q25leHQlNDAxNC4yLjMwX3JlYWN0LWRvbSU0MDE4LjMuMV9yZWFjdCU0MDE4LjMuMV9fcmVhY3QlNDAxOC4zLjElNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2NvbXBvbmVudHMlNUMlNUNyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxrWUFBME07QUFDMU07QUFDQSxvWUFBMk07QUFDM007QUFDQSwwWUFBOE07QUFDOU07QUFDQSx3WUFBNk07QUFDN007QUFDQSxrWkFBa047QUFDbE47QUFDQSxzYUFBNE4iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9AeXVueXUvbWFpbi8/NjJkYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkU6XFxcXFdvcmtzcGFjZVxcXFx5eS16c1xcXFx3ZWJcXFxcbm9kZV9tb2R1bGVzXFxcXC5wbnBtXFxcXG5leHRAMTQuMi4zMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGNvbXBvbmVudHNcXFxcYXBwLXJvdXRlci5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcV29ya3NwYWNlXFxcXHl5LXpzXFxcXHdlYlxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxcbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjFcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxjbGllbnQtcGFnZS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcV29ya3NwYWNlXFxcXHl5LXpzXFxcXHdlYlxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxcbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjFcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxlcnJvci1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcV29ya3NwYWNlXFxcXHl5LXpzXFxcXHdlYlxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxcbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjFcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxsYXlvdXQtcm91dGVyLmpzXCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxXb3Jrc3BhY2VcXFxceXktenNcXFxcd2ViXFxcXG5vZGVfbW9kdWxlc1xcXFwucG5wbVxcXFxuZXh0QDE0LjIuMzBfcmVhY3QtZG9tQDE4LjMuMV9yZWFjdEAxOC4zLjFfX3JlYWN0QDE4LjMuMVxcXFxub2RlX21vZHVsZXNcXFxcbmV4dFxcXFxkaXN0XFxcXGNsaWVudFxcXFxjb21wb25lbnRzXFxcXG5vdC1mb3VuZC1ib3VuZGFyeS5qc1wiKTtcbjtcbmltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcV29ya3NwYWNlXFxcXHl5LXpzXFxcXHdlYlxcXFxub2RlX21vZHVsZXNcXFxcLnBucG1cXFxcbmV4dEAxNC4yLjMwX3JlYWN0LWRvbUAxOC4zLjFfcmVhY3RAMTguMy4xX19yZWFjdEAxOC4zLjFcXFxcbm9kZV9tb2R1bGVzXFxcXG5leHRcXFxcZGlzdFxcXFxjbGllbnRcXFxcY29tcG9uZW50c1xcXFxyZW5kZXItZnJvbS10ZW1wbGF0ZS1jb250ZXh0LmpzXCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/app/network/layout.tsx":
/*!************************************!*\
  !*** ./src/app/network/layout.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NetworkSystemLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Home_Network_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Home,Network,Shield!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/home.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Home_Network_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Home,Network,Shield!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Home_Network_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Home,Network,Shield!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Home_Network_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Home,Network,Shield!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_Globe_Home_Network_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,Globe,Home,Network,Shield!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\nfunction NetworkSystemLayout({ children }) {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const menuItems = [\n        {\n            id: \"home\",\n            label: \"首页\",\n            icon: _barrel_optimize_names_BarChart3_Globe_Home_Network_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            href: \"/network\"\n        },\n        {\n            id: \"management\",\n            label: \"网络管理\",\n            icon: _barrel_optimize_names_BarChart3_Globe_Home_Network_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            href: \"/network/management\"\n        },\n        {\n            id: \"topology\",\n            label: \"网络拓扑\",\n            icon: _barrel_optimize_names_BarChart3_Globe_Home_Network_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            href: \"/network/topology\"\n        },\n        {\n            id: \"traffic\",\n            label: \"流量监控\",\n            icon: _barrel_optimize_names_BarChart3_Globe_Home_Network_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            href: \"/network/traffic\"\n        },\n        {\n            id: \"security\",\n            label: \"安全管理\",\n            icon: _barrel_optimize_names_BarChart3_Globe_Home_Network_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            href: \"/network/security\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_Globe_Home_Network_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                                                    lineNumber: 57,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                                                lineNumber: 56,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                                        children: \"\\uD83C\\uDF10 网络管理系统\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                                                        lineNumber: 60,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"多物理网络管理与安全监控平台\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                                                        lineNumber: 63,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                                                lineNumber: 59,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: menuItems.map((item)=>{\n                                            const Icon = item.icon;\n                                            const isActive = pathname === item.href;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                href: item.href,\n                                                className: `flex items-center space-x-2 px-4 py-2 rounded-lg transition-all duration-200 font-medium ${isActive ? \"bg-blue-100 text-blue-700 shadow-sm\" : \"text-gray-700 hover:text-blue-600 hover:bg-blue-50/50\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                                                        lineNumber: 82,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: item.label\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                                                        lineNumber: 83,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, item.id, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                                                lineNumber: 73,\n                                                columnNumber: 21\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                                        lineNumber: 68,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                                lineNumber: 53,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        className: \"text-gray-600 hover:text-blue-600 transition-colors text-sm font-medium\",\n                                        children: \"返回首页\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                                        lineNumber: 92,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/dashboard\",\n                                        className: \"bg-blue-600 text-white px-4 py-2 rounded-xl hover:bg-blue-700 transition-colors text-sm font-medium\",\n                                        children: \"主控台\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                        lineNumber: 52,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full px-6 py-8\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                lineNumber: 110,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 pointer-events-none overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-float\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-indigo-400/20 to-blue-400/20 rounded-full blur-3xl animate-float\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\layout.tsx\",\n        lineNumber: 48,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/network/layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/network/page.tsx":
/*!**********************************!*\
  !*** ./src/app/network/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NetworkSystemPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,CheckCircle,Network,Plus,Router,Shield,XCircle!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,CheckCircle,Network,Plus,Router,Shield,XCircle!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/router.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,CheckCircle,Network,Plus,Router,Shield,XCircle!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,CheckCircle,Network,Plus,Router,Shield,XCircle!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,CheckCircle,Network,Plus,Router,Shield,XCircle!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,CheckCircle,Network,Plus,Router,Shield,XCircle!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,CheckCircle,Network,Plus,Router,Shield,XCircle!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,CheckCircle,Network,Plus,Router,Shield,XCircle!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,CheckCircle,Network,Plus,Router,Shield,XCircle!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,ArrowRight,BarChart3,CheckCircle,Network,Plus,Router,Shield,XCircle!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction NetworkSystemPage() {\n    const networks = [\n        {\n            id: 1,\n            name: \"政务内网\",\n            type: \"内部网络\",\n            subnet: \"192.168.1.0/24\",\n            status: \"active\",\n            devices: 156,\n            bandwidth: \"1Gbps\",\n            utilization: 45,\n            security: \"high\",\n            location: \"主机房\"\n        },\n        {\n            id: 2,\n            name: \"政务外网\",\n            type: \"外部网络\",\n            subnet: \"10.0.1.0/24\",\n            status: \"active\",\n            devices: 89,\n            bandwidth: \"500Mbps\",\n            utilization: 72,\n            security: \"medium\",\n            location: \"主机房\"\n        },\n        {\n            id: 3,\n            name: \"专网接入\",\n            type: \"专用网络\",\n            subnet: \"172.16.1.0/24\",\n            status: \"warning\",\n            devices: 24,\n            bandwidth: \"100Mbps\",\n            utilization: 88,\n            security: \"high\",\n            location: \"分机房A\"\n        },\n        {\n            id: 4,\n            name: \"备份网络\",\n            type: \"备份网络\",\n            subnet: \"192.168.100.0/24\",\n            status: \"offline\",\n            devices: 12,\n            bandwidth: \"100Mbps\",\n            utilization: 0,\n            security: \"medium\",\n            location: \"分机房B\"\n        }\n    ];\n    const quickStats = [\n        {\n            label: \"网络数量\",\n            value: \"8\",\n            trend: \"+1\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            color: \"blue\"\n        },\n        {\n            label: \"在线设备\",\n            value: \"256\",\n            trend: \"+12\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"indigo\"\n        },\n        {\n            label: \"总带宽\",\n            value: \"2.5Gbps\",\n            trend: \"+500M\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"green\"\n        },\n        {\n            label: \"安全事件\",\n            value: \"2\",\n            trend: \"-3\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"cyan\"\n        }\n    ];\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"active\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-5 h-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 29\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-5 h-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 30\n                }, this);\n            case \"offline\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-5 h-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 30\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-5 h-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"active\":\n                return \"正常\";\n            case \"warning\":\n                return \"告警\";\n            case \"offline\":\n                return \"离线\";\n            default:\n                return \"未知\";\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"active\":\n                return \"bg-green-100 text-green-700\";\n            case \"warning\":\n                return \"bg-yellow-100 text-yellow-700\";\n            case \"offline\":\n                return \"bg-red-100 text-red-700\";\n            default:\n                return \"bg-gray-100 text-gray-700\";\n        }\n    };\n    const getSecurityIcon = (security)=>{\n        switch(security){\n            case \"high\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 27\n                }, this);\n            case \"medium\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 29\n                }, this);\n            case \"low\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 26\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getSecurityText = (security)=>{\n        switch(security){\n            case \"high\":\n                return \"高\";\n            case \"medium\":\n                return \"中\";\n            case \"low\":\n                return \"低\";\n            default:\n                return \"未知\";\n        }\n    };\n    const getUtilizationColor = (utilization)=>{\n        if (utilization >= 90) return \"bg-red-500\";\n        if (utilization >= 70) return \"bg-yellow-500\";\n        return \"bg-green-500\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-gray-900 mb-4\",\n                        children: \"网络管理首页\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600\",\n                        children: \"管理多物理网络，监控网络拓扑和流量，确保网络安全\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: quickStats.map((stat, index)=>{\n                    const Icon = stat.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-600 font-medium\",\n                                            children: stat.trend\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                            lineNumber: 148,\n                            columnNumber: 15\n                        }, this)\n                    }, stat.label, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"bg-blue-600 text-white px-6 py-3 rounded-xl hover:bg-blue-700 transition-colors flex items-center space-x-2 font-medium\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"添加网络\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                    lineNumber: 168,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"网络拓扑\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                    lineNumber: 172,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                            lineNumber: 170,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                lineNumber: 164,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-6\",\n                children: networks.map((network, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 group\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-6\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-3 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-bold text-gray-900\",\n                                                            children: network.name\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(network.status)}`,\n                                                            children: getStatusText(network.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        getStatusIcon(network.status)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-4 text-sm text-gray-600 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"bg-gray-100 px-2 py-1 rounded-full\",\n                                                            children: network.type\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: network.subnet\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full\",\n                                                    children: network.location\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                className: \"w-6 h-6 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                    lineNumber: 186,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 gap-4 mb-4 p-4 bg-gray-50/50 rounded-xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: \"连接设备\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-lg font-bold text-gray-900\",\n                                                    children: network.devices\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-xs text-gray-600 mb-1\",\n                                                    children: \"带宽\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm font-medium text-gray-900\",\n                                                    children: network.bandwidth\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                    lineNumber: 214,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                            lineNumber: 212,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-sm mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-4 h-4 text-gray-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: \"带宽利用率\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                            lineNumber: 223,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                    lineNumber: 221,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-medium\",\n                                                    children: [\n                                                        network.utilization,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-gray-200 rounded-full h-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `h-2 rounded-full transition-all duration-500 ${getUtilizationColor(network.utilization)}`,\n                                                style: {\n                                                    width: `${network.utilization}%`\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                    lineNumber: 219,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between text-sm text-gray-500 mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-1\",\n                                        children: [\n                                            getSecurityIcon(network.security),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: [\n                                                    \"安全级别: \",\n                                                    getSecurityText(network.security)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                    lineNumber: 236,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors\",\n                                                    children: \"配置\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                    lineNumber: 246,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors\",\n                                                    children: \"流量分析\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                    lineNumber: 249,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                            href: `/network/detail/${network.id}`,\n                                            className: \"text-blue-600 hover:text-blue-700 font-medium flex items-center space-x-1 transition-colors text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"详情\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                    lineNumber: 257,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_ArrowRight_BarChart3_CheckCircle_Network_Plus_Router_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"w-4 h-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                                    lineNumber: 258,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this)\n                    }, network.id, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\network\\\\page.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/network/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0d5fe442f342\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHl1bnl1L21haW4vLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2U0MmYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwZDVmZTQ0MmYzNDJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"云宇政数平台\",\n    description: \"统一的政数局数据管理平台 - 大屏展示、报表分析、数据采集、数据汇聚、数据清洗治理、数据资源池管理、设备监控\",\n    keywords: \"政数局,数据平台,大屏,报表,数据采集,数据治理,设备监控\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans\",\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO0FBQ1osRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFVO3NCQUNiSjs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL0B5dW55dS9tYWluLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAn5LqR5a6H5pS/5pWw5bmz5Y+wJyxcbiAgZGVzY3JpcHRpb246ICfnu5/kuIDnmoTmlL/mlbDlsYDmlbDmja7nrqHnkIblubPlj7AgLSDlpKflsY/lsZXnpLrjgIHmiqXooajliIbmnpDjgIHmlbDmja7ph4fpm4bjgIHmlbDmja7msYfogZrjgIHmlbDmja7muIXmtJfmsrvnkIbjgIHmlbDmja7otYTmupDmsaDnrqHnkIbjgIHorr7lpIfnm5HmjqcnLFxuICBrZXl3b3JkczogJ+aUv+aVsOWxgCzmlbDmja7lubPlj7As5aSn5bGPLOaKpeihqCzmlbDmja7ph4fpm4Ys5pWw5o2u5rK755CGLOiuvuWkh+ebkeaOpycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiZm9udC1zYW5zXCI+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/network/layout.tsx":
/*!************************************!*\
  !*** ./src/app/network/layout.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Workspace\yy-zs\web\apps\main\src\app\network\layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/network/page.tsx":
/*!**********************************!*\
  !*** ./src/app/network/page.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Workspace\yy-zs\web\apps\main\src\app\network\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/@swc+helpers@0.5.5","vendor-chunks/lucide-react@0.294.0_react@18.3.1"], () => (__webpack_exec__("(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnetwork%2Fpage&page=%2Fnetwork%2Fpage&appPaths=%2Fnetwork%2Fpage&pagePath=private-next-app-dir%2Fnetwork%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
'use client'

import Link from 'next/link'
import { 
  ArrowLeft,
  Server,
  Plus,
  Search,
  Filter,
  Download,
  Upload,
  Edit,
  Trash2,
  Eye,
  Power,
  RotateCcw,
  Settings,
  Calendar,
  MapPin,
  User,
  Tag,
  Cpu,
  MemoryStick,
  HardDrive,
  Network,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Building,
  Wifi
} from 'lucide-react'

export default function DeviceManagementPage() {
  // 设备统计
  const deviceStats = [
    { label: '设备总数', value: '156', icon: Server, color: 'blue' },
    { label: '在线设备', value: '142', icon: CheckCircle, color: 'green' },
    { label: '离线设备', value: '14', icon: XCircle, color: 'red' },
    { label: '维护中', value: '8', icon: Settings, color: 'orange' }
  ]

  // 设备类型分布
  const deviceTypes = [
    { type: 'Web服务器', count: 45, icon: Server, color: 'blue' },
    { type: '数据库服务器', count: 32, icon: Server, color: 'green' },
    { type: '应用服务器', count: 38, icon: Server, color: 'purple' },
    { type: '文件服务器', count: 25, icon: Server, color: 'orange' },
    { type: '其他设备', count: 16, icon: Server, color: 'gray' }
  ]

  // 设备列表
  const devices = [
    {
      id: 1,
      name: 'Web服务器-01',
      type: 'Web服务器',
      ip: '************',
      location: '机房A-01',
      status: 'online',
      os: 'Ubuntu 20.04',
      cpu: '8核 Intel Xeon',
      memory: '32GB',
      disk: '500GB SSD',
      network: '1Gbps',
      uptime: '15天 8小时',
      lastMaintenance: '2024-01-01',
      responsible: '张三',
      tags: ['生产环境', 'Web服务'],
      health: 95
    },
    {
      id: 2,
      name: '数据库服务器-01',
      type: '数据库服务器',
      ip: '************',
      location: '机房A-02',
      status: 'warning',
      os: 'CentOS 7',
      cpu: '16核 Intel Xeon',
      memory: '64GB',
      disk: '1TB SSD',
      network: '10Gbps',
      uptime: '22天 14小时',
      lastMaintenance: '2023-12-15',
      responsible: '李四',
      tags: ['生产环境', '数据库'],
      health: 78
    },
    {
      id: 3,
      name: '应用服务器-01',
      type: '应用服务器',
      ip: '************',
      location: '机房B-01',
      status: 'online',
      os: 'Ubuntu 22.04',
      cpu: '12核 AMD EPYC',
      memory: '48GB',
      disk: '800GB SSD',
      network: '1Gbps',
      uptime: '8天 3小时',
      lastMaintenance: '2024-01-10',
      responsible: '王五',
      tags: ['生产环境', '应用服务'],
      health: 92
    },
    {
      id: 4,
      name: '文件服务器-01',
      type: '文件服务器',
      ip: '************',
      location: '机房B-02',
      status: 'offline',
      os: 'Windows Server 2019',
      cpu: '8核 Intel Xeon',
      memory: '32GB',
      disk: '2TB HDD',
      network: '1Gbps',
      uptime: '0天 0小时',
      lastMaintenance: '2023-11-20',
      responsible: '赵六',
      tags: ['生产环境', '文件存储'],
      health: 45
    },
    {
      id: 5,
      name: '测试服务器-01',
      type: '应用服务器',
      ip: '************',
      location: '机房C-01',
      status: 'maintenance',
      os: 'Ubuntu 20.04',
      cpu: '4核 Intel i7',
      memory: '16GB',
      disk: '256GB SSD',
      network: '1Gbps',
      uptime: '0天 0小时',
      lastMaintenance: '2024-01-15',
      responsible: '钱七',
      tags: ['测试环境', '开发'],
      health: 88
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-600 bg-green-100'
      case 'offline': return 'text-red-600 bg-red-100'
      case 'warning': return 'text-orange-600 bg-orange-100'
      case 'maintenance': return 'text-blue-600 bg-blue-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getHealthColor = (health: number) => {
    if (health >= 90) return 'text-green-600 bg-green-100'
    if (health >= 70) return 'text-orange-600 bg-orange-100'
    return 'text-red-600 bg-red-100'
  }

  return (
    <div className="w-full px-4 py-8 space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/monitoring" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-3">
              <Server className="w-8 h-8 text-purple-600" />
              <span>设备管理</span>
            </h1>
            <p className="mt-1 text-gray-600">设备台账管理，设备信息维护和设备生命周期管理</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <button className="btn-secondary flex items-center space-x-2">
            <Download className="w-4 h-4" />
            <span>导出台账</span>
          </button>
          <button className="btn-secondary flex items-center space-x-2">
            <Upload className="w-4 h-4" />
            <span>批量导入</span>
          </button>
          <button className="btn-primary flex items-center space-x-2">
            <Plus className="w-4 h-4" />
            <span>添加设备</span>
          </button>
        </div>
      </div>

      {/* 设备统计 */}
      <section className="space-y-6">
        <h2 className="text-xl font-bold text-gray-900">设备概览</h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {deviceStats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <div key={stat.label} className="card">
                <div className="card-content">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="text-sm text-gray-600">{stat.label}</div>
                      <div className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</div>
                    </div>
                    <div className={`w-12 h-12 rounded-2xl flex items-center justify-center ${
                      stat.color === 'blue' ? 'bg-blue-100' :
                      stat.color === 'green' ? 'bg-green-100' :
                      stat.color === 'red' ? 'bg-red-100' : 'bg-orange-100'
                    }`}>
                      <Icon className={`w-6 h-6 ${
                        stat.color === 'blue' ? 'text-blue-600' :
                        stat.color === 'green' ? 'text-green-600' :
                        stat.color === 'red' ? 'text-red-600' : 'text-orange-600'
                      }`} />
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </section>

      {/* 设备类型分布 */}
      <section className="space-y-6">
        <h2 className="text-xl font-bold text-gray-900">设备类型分布</h2>
        
        <div className="card">
          <div className="card-content">
            <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
              {deviceTypes.map((type, index) => {
                const Icon = type.icon
                return (
                  <div key={type.type} className="text-center">
                    <div className={`w-16 h-16 rounded-2xl mx-auto mb-3 flex items-center justify-center ${
                      type.color === 'blue' ? 'bg-blue-100' :
                      type.color === 'green' ? 'bg-green-100' :
                      type.color === 'purple' ? 'bg-purple-100' :
                      type.color === 'orange' ? 'bg-orange-100' : 'bg-gray-100'
                    }`}>
                      <Icon className={`w-8 h-8 ${
                        type.color === 'blue' ? 'text-blue-600' :
                        type.color === 'green' ? 'text-green-600' :
                        type.color === 'purple' ? 'text-purple-600' :
                        type.color === 'orange' ? 'text-orange-600' : 'text-gray-600'
                      }`} />
                    </div>
                    <div className="text-2xl font-bold text-gray-900">{type.count}</div>
                    <div className="text-sm text-gray-600">{type.type}</div>
                  </div>
                )
              })}
            </div>
          </div>
        </div>
      </section>

      {/* 设备列表 */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">设备台账</h2>
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="搜索设备..."
                className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <button className="btn-secondary flex items-center space-x-2">
              <Filter className="w-4 h-4" />
              <span>筛选</span>
            </button>
          </div>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="space-y-6">
              {devices.map((device) => (
                <div key={device.id} className="border border-gray-100 rounded-xl p-6 hover:bg-gray-50/50 transition-colors">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start space-x-4">
                      <div className={`w-12 h-12 rounded-2xl flex items-center justify-center ${
                        device.status === 'online' ? 'bg-green-100' :
                        device.status === 'offline' ? 'bg-red-100' :
                        device.status === 'warning' ? 'bg-orange-100' : 'bg-blue-100'
                      }`}>
                        <Server className={`w-6 h-6 ${
                          device.status === 'online' ? 'text-green-600' :
                          device.status === 'offline' ? 'text-red-600' :
                          device.status === 'warning' ? 'text-orange-600' : 'text-blue-600'
                        }`} />
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900">{device.name}</h4>
                        <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                          <span className="flex items-center space-x-1">
                            <Network className="w-3 h-3" />
                            <span>{device.ip}</span>
                          </span>
                          <span className="flex items-center space-x-1">
                            <MapPin className="w-3 h-3" />
                            <span>{device.location}</span>
                          </span>
                          <span className="flex items-center space-x-1">
                            <User className="w-3 h-3" />
                            <span>{device.responsible}</span>
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <span className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusColor(device.status)}`}>
                        {device.status === 'online' ? '在线' :
                         device.status === 'offline' ? '离线' :
                         device.status === 'warning' ? '警告' : '维护中'}
                      </span>
                      <span className={`px-3 py-1 text-xs font-medium rounded-full ${getHealthColor(device.health)}`}>
                        健康度 {device.health}%
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-4">
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">基本信息</h5>
                      <div className="space-y-1 text-sm text-gray-600">
                        <div>类型: {device.type}</div>
                        <div>系统: {device.os}</div>
                        <div>运行时间: {device.uptime}</div>
                      </div>
                    </div>
                    
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">硬件配置</h5>
                      <div className="space-y-1 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <Cpu className="w-3 h-3" />
                          <span>{device.cpu}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MemoryStick className="w-3 h-3" />
                          <span>{device.memory}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <HardDrive className="w-3 h-3" />
                          <span>{device.disk}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">网络信息</h5>
                      <div className="space-y-1 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <Wifi className="w-3 h-3" />
                          <span>{device.network}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Building className="w-3 h-3" />
                          <span>{device.location}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">维护信息</h5>
                      <div className="space-y-1 text-sm text-gray-600">
                        <div className="flex items-center space-x-1">
                          <Calendar className="w-3 h-3" />
                          <span>上次维护: {device.lastMaintenance}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <User className="w-3 h-3" />
                          <span>负责人: {device.responsible}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      {device.tags.map((tag, index) => (
                        <span key={index} className="px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded-full flex items-center space-x-1">
                          <Tag className="w-3 h-3" />
                          <span>{tag}</span>
                        </span>
                      ))}
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                        <Edit className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors">
                        <RotateCcw className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                        <Power className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

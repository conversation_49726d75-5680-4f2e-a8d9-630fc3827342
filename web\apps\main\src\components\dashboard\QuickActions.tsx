'use client'

import { Plus, Download, Upload, Settings, RefreshCw, AlertCircle } from 'lucide-react'

const quickActions = [
  {
    name: '新建报表',
    description: '创建新的数据分析报表',
    icon: Plus,
    action: () => console.log('新建报表'),
  },
  {
    name: '数据导入',
    description: '导入外部数据源',
    icon: Upload,
    action: () => console.log('数据导入'),
  },
  {
    name: '导出数据',
    description: '导出当前数据',
    icon: Download,
    action: () => console.log('导出数据'),
  },
  {
    name: '系统设置',
    description: '配置系统参数',
    icon: Settings,
    action: () => console.log('系统设置'),
  },
  {
    name: '刷新缓存',
    description: '清理系统缓存',
    icon: RefreshCw,
    action: () => console.log('刷新缓存'),
  },
  {
    name: '查看日志',
    description: '查看系统运行日志',
    icon: AlertCircle,
    action: () => console.log('查看日志'),
  },
]

export function QuickActions() {
  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold text-gray-900">快捷操作</h3>
      </div>
      <div className="card-content">
        <div className="space-y-3">
          {quickActions.map((action) => {
            const Icon = action.icon
            return (
              <button
                key={action.name}
                onClick={action.action}
                className="w-full flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors text-left"
              >
                <div className="p-2 bg-primary-100 rounded-lg">
                  <Icon className="w-4 h-4 text-primary-600" />
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">
                    {action.name}
                  </div>
                  <div className="text-xs text-gray-500">
                    {action.description}
                  </div>
                </div>
              </button>
            )
          })}
        </div>
      </div>
    </div>
  )
}

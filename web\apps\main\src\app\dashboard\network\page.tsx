export default function NetworkPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">网络管理</h1>
          <p className="mt-1 text-gray-600">多物理网络管理与流量监控</p>
        </div>
        <button className="btn-primary">
          添加网络
        </button>
      </div>

      {/* 网络概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[
          { label: '网络总数', value: '3', unit: '个' },
          { label: '在线设备', value: '127', unit: '台' },
          { label: '总带宽', value: '10', unit: 'Gbps' },
          { label: '平均利用率', value: '45', unit: '%' },
        ].map((stat) => (
          <div key={stat.label} className="card">
            <div className="card-content text-center">
              <div className="text-2xl font-bold text-primary-600">
                {stat.value}
                <span className="text-sm text-gray-500 ml-1">{stat.unit}</span>
              </div>
              <div className="text-sm text-gray-600 mt-1">{stat.label}</div>
            </div>
          </div>
        ))}
      </div>

      {/* 网络拓扑 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">网络拓扑</h3>
        </div>
        <div className="card-content">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {[
              { 
                name: '政务外网', 
                type: '外部网络', 
                devices: 45, 
                bandwidth: '1Gbps', 
                utilization: 35,
                status: '正常'
              },
              { 
                name: '政务内网', 
                type: '内部网络', 
                devices: 67, 
                bandwidth: '5Gbps', 
                utilization: 52,
                status: '正常'
              },
              { 
                name: '专用网络', 
                type: '专网', 
                devices: 15, 
                bandwidth: '4Gbps', 
                utilization: 28,
                status: '维护中'
              },
            ].map((network, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-3">
                  <div>
                    <h4 className="font-medium text-gray-900">{network.name}</h4>
                    <div className="text-sm text-gray-500">{network.type}</div>
                  </div>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    network.status === '正常' ? 'bg-success-100 text-success-700' :
                    network.status === '维护中' ? 'bg-warning-100 text-warning-700' :
                    'bg-error-100 text-error-700'
                  }`}>
                    {network.status}
                  </span>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">设备数量:</span>
                    <span className="text-gray-900">{network.devices}台</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">总带宽:</span>
                    <span className="text-gray-900">{network.bandwidth}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">利用率:</span>
                    <span className="text-gray-900">{network.utilization}%</span>
                  </div>
                </div>
                
                <div className="mt-3">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${
                        network.utilization > 80 ? 'bg-error-500' :
                        network.utilization > 60 ? 'bg-warning-500' :
                        'bg-success-500'
                      }`}
                      style={{ width: `${network.utilization}%` }}
                    ></div>
                  </div>
                </div>
                
                <div className="mt-3 flex space-x-2">
                  <button className="text-primary-600 hover:text-primary-700 text-sm">详情</button>
                  <button className="text-gray-600 hover:text-gray-700 text-sm">配置</button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 流量监控 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">实时流量监控</h3>
        </div>
        <div className="card-content">
          <div className="h-64 bg-gray-50 rounded-lg flex items-center justify-center">
            <div className="text-center text-gray-500">
              <div className="text-lg font-medium">流量监控图表</div>
              <div className="text-sm mt-1">实时网络流量数据可视化</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

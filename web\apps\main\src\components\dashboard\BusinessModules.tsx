'use client'

import Link from 'next/link'
import { 
  Monitor, 
  FileText, 
  Database, 
  GitMerge, 
  Shield, 
  HardDrive, 
  Server,
  Network,
  ArrowRight
} from 'lucide-react'

const modules = [
  {
    name: '数据大屏',
    description: '实时数据可视化展示，支持多种图表和指标监控',
    href: '/dashboard/screen',
    icon: Monitor,
    gradient: 'from-blue-500 to-cyan-500',
    stats: '12个大屏',
    trend: '+15%',
  },
  {
    name: '报表分析',
    description: '多维度数据分析报表，支持自定义报表生成',
    href: '/dashboard/reports',
    icon: FileText,
    gradient: 'from-green-500 to-emerald-500',
    stats: '156份报表',
    trend: '+8%',
  },
  {
    name: '数据采集',
    description: '多源数据采集接入，支持实时和批量数据导入',
    href: '/dashboard/collection',
    icon: Database,
    gradient: 'from-purple-500 to-violet-500',
    stats: '24个数据源',
    trend: '+12%',
  },
  {
    name: '数据汇聚',
    description: '跨系统数据整合汇聚，统一数据标准和格式',
    href: '/dashboard/aggregation',
    icon: GitMerge,
    gradient: 'from-orange-500 to-amber-500',
    stats: '8个汇聚任务',
    trend: '+5%',
  },
  {
    name: '数据治理',
    description: '数据质量管控，数据清洗、去重、标准化处理',
    href: '/dashboard/governance',
    icon: Shield,
    gradient: 'from-red-500 to-rose-500',
    stats: '质量评分92%',
    trend: '+3%',
  },
  {
    name: '资源池管理',
    description: '数据资源统一管理，资源目录和权限控制',
    href: '/dashboard/resources',
    icon: HardDrive,
    gradient: 'from-indigo-500 to-blue-500',
    stats: '2.4TB存储',
    trend: '+18%',
  },
  {
    name: '设备监控',
    description: '服务器设备实时监控，性能指标和告警管理',
    href: '/dashboard/monitoring',
    icon: Server,
    gradient: 'from-teal-500 to-cyan-500',
    stats: '45台设备',
    trend: '+2%',
  },
  {
    name: '网络管理',
    description: '多物理网络管理，网络拓扑和流量监控',
    href: '/dashboard/network',
    icon: Network,
    gradient: 'from-pink-500 to-rose-500',
    stats: '3个网络',
    trend: '稳定',
  },
]

export function BusinessModules() {
  return (
    <div className="space-y-8">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold gradient-text">业务模块</h2>
          <p className="text-gray-600 mt-1">点击进入对应业务处理模块</p>
        </div>
        <div className="hidden md:flex items-center space-x-2 text-sm text-gray-500">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span>系统运行正常</span>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {modules.map((module, index) => {
          const Icon = module.icon
          return (
            <Link key={module.name} href={module.href}>
              <div
                className="card hover:shadow-glow transition-all duration-500 cursor-pointer group transform hover:scale-105 animate-slide-up"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="card-content relative overflow-hidden">
                  {/* 背景渐变效果 */}
                  <div className={`absolute inset-0 bg-gradient-to-br ${module.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}></div>

                  <div className="relative z-10">
                    <div className="flex items-start justify-between mb-6">
                      <div className={`p-4 rounded-2xl bg-gradient-to-br ${module.gradient} shadow-lg group-hover:shadow-glow transition-all duration-300 transform group-hover:scale-110`}>
                        <Icon className="w-6 h-6 text-white" />
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-xs font-medium text-green-600 bg-green-50 px-2 py-1 rounded-full">
                          {module.trend}
                        </span>
                        <ArrowRight className="w-4 h-4 text-gray-400 group-hover:text-blue-600 transition-all duration-300 transform group-hover:translate-x-1" />
                      </div>
                    </div>

                    <h3 className="text-lg font-bold text-gray-900 mb-3 group-hover:text-blue-700 transition-colors">
                      {module.name}
                    </h3>

                    <p className="text-sm text-gray-600 mb-6 leading-relaxed">
                      {module.description}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        <span className="text-sm font-medium text-gray-700">{module.stats}</span>
                      </div>
                      <span className="text-sm font-semibold text-blue-600 group-hover:text-blue-700 transition-colors">
                        进入 →
                      </span>
                    </div>
                  </div>

                  {/* 悬浮时的光效 */}
                  <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                    <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white/50 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                  </div>
                </div>
              </div>
            </Link>
          )
        })}
      </div>
    </div>
  )
}

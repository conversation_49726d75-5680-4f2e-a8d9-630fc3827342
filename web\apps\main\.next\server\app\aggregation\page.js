/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/aggregation/page";
exports.ids = ["app/aggregation/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Faggregation%2Fpage&page=%2Faggregation%2Fpage&appPaths=%2Faggregation%2Fpage&pagePath=private-next-app-dir%2Faggregation%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Faggregation%2Fpage&page=%2Faggregation%2Fpage&appPaths=%2Faggregation%2Fpage&pagePath=private-next-app-dir%2Faggregation%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?3032\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'aggregation',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/aggregation/page.tsx */ \"(rsc)/./src/app/aggregation/page.tsx\")), \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/aggregation/layout.tsx */ \"(rsc)/./src/app/aggregation/layout.tsx\")), \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/aggregation/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/aggregation/page\",\n        pathname: \"/aggregation\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Faggregation%2Fpage&page=%2Faggregation%2Fpage&appPaths=%2Faggregation%2Fpage&pagePath=private-next-app-dir%2Faggregation%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Caggregation%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Caggregation%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/aggregation/layout.tsx */ \"(ssr)/./src/app/aggregation/layout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL25leHRAMTQuMi4zMF9yZWFjdC1kb21AMTguMy4xX3JlYWN0QDE4LjMuMV9fcmVhY3RAMTguMy4xL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRSUzQSU1QyU1Q1dvcmtzcGFjZSU1QyU1Q3l5LXpzJTVDJTVDd2ViJTVDJTVDYXBwcyU1QyU1Q21haW4lNUMlNUNzcmMlNUMlNUNhcHAlNUMlNUNhZ2dyZWdhdGlvbiU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDRLQUE2RyIsInNvdXJjZXMiOlsid2VicGFjazovL0B5dW55dS9tYWluLz8wZDk5Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRTpcXFxcV29ya3NwYWNlXFxcXHl5LXpzXFxcXHdlYlxcXFxhcHBzXFxcXG1haW5cXFxcc3JjXFxcXGFwcFxcXFxhZ2dyZWdhdGlvblxcXFxsYXlvdXQudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Caggregation%5C%5Clayout.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Capps%5C%5Cmain%5C%5Csrc%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CWorkspace%5C%5Cyy-zs%5C%5Cweb%5C%5Cnode_modules%5C%5C.pnpm%5C%5Cnext%4014.2.30_react-dom%4018.3.1_react%4018.3.1__react%4018.3.1%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!**************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \**************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/aggregation/components/ChannelManagement.tsx":
/*!**************************************************************!*\
  !*** ./src/app/aggregation/components/ChannelManagement.tsx ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ChannelManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,CheckCircle,Clock,Cpu,Database,FileText,HardDrive,Layers,Network,Server,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,CheckCircle,Clock,Cpu,Database,FileText,HardDrive,Layers,Network,Server,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,CheckCircle,Clock,Cpu,Database,FileText,HardDrive,Layers,Network,Server,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,CheckCircle,Clock,Cpu,Database,FileText,HardDrive,Layers,Network,Server,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/layers.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,CheckCircle,Clock,Cpu,Database,FileText,HardDrive,Layers,Network,Server,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,CheckCircle,Clock,Cpu,Database,FileText,HardDrive,Layers,Network,Server,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,CheckCircle,Clock,Cpu,Database,FileText,HardDrive,Layers,Network,Server,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,CheckCircle,Clock,Cpu,Database,FileText,HardDrive,Layers,Network,Server,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,CheckCircle,Clock,Cpu,Database,FileText,HardDrive,Layers,Network,Server,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,CheckCircle,Clock,Cpu,Database,FileText,HardDrive,Layers,Network,Server,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/activity.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,CheckCircle,Clock,Cpu,Database,FileText,HardDrive,Layers,Network,Server,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,CheckCircle,Clock,Cpu,Database,FileText,HardDrive,Layers,Network,Server,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,CheckCircle,Clock,Cpu,Database,FileText,HardDrive,Layers,Network,Server,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Activity,AlertTriangle,BarChart3,CheckCircle,Clock,Cpu,Database,FileText,HardDrive,Layers,Network,Server,Settings,Zap!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction ChannelManagement() {\n    const channels = [\n        {\n            id: 1,\n            name: \"Kafka 集群\",\n            type: \"kafka\",\n            status: \"healthy\",\n            description: \"高吞吐量消息队列，处理实时数据流\",\n            nodes: 3,\n            throughput: \"2.5K msg/s\",\n            storage: \"1.2TB\",\n            uptime: \"99.9%\",\n            cpu: 45,\n            memory: 68,\n            network: 32,\n            partitions: 24,\n            consumers: 8\n        },\n        {\n            id: 2,\n            name: \"RabbitMQ 集群\",\n            type: \"rabbitmq\",\n            status: \"healthy\",\n            description: \"可靠消息传递，支持复杂路由\",\n            nodes: 2,\n            throughput: \"1.8K msg/s\",\n            storage: \"450GB\",\n            uptime: \"99.8%\",\n            cpu: 38,\n            memory: 55,\n            network: 28,\n            queues: 16,\n            exchanges: 12\n        },\n        {\n            id: 3,\n            name: \"Redis 缓存\",\n            type: \"redis\",\n            status: \"warning\",\n            description: \"高性能内存数据库，用于缓存和会话存储\",\n            nodes: 2,\n            throughput: \"15K ops/s\",\n            storage: \"128GB\",\n            uptime: \"99.5%\",\n            cpu: 72,\n            memory: 85,\n            network: 45,\n            keys: \"2.4M\",\n            hitRate: \"94.2%\"\n        },\n        {\n            id: 4,\n            name: \"中间库数据库\",\n            type: \"database\",\n            status: \"healthy\",\n            description: \"PostgreSQL 集群，存储处理后的结构化数据\",\n            nodes: 2,\n            throughput: \"850 TPS\",\n            storage: \"5.6TB\",\n            uptime: \"99.9%\",\n            cpu: 52,\n            memory: 71,\n            network: 35,\n            connections: 245,\n            tables: 156\n        },\n        {\n            id: 5,\n            name: \"FTP 文件服务\",\n            type: \"ftp\",\n            status: \"error\",\n            description: \"文件传输服务，处理大文件和批量数据\",\n            nodes: 1,\n            throughput: \"120 MB/s\",\n            storage: \"2.8TB\",\n            uptime: \"98.2%\",\n            cpu: 25,\n            memory: 42,\n            network: 78,\n            files: \"45K\",\n            transfers: 156\n        }\n    ];\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"healthy\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    className: \"w-5 h-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                    lineNumber: 106,\n                    columnNumber: 30\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-5 h-5 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 30\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                    className: \"w-5 h-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 28\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    className: \"w-5 h-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"healthy\":\n                return \"健康\";\n            case \"warning\":\n                return \"警告\";\n            case \"error\":\n                return \"错误\";\n            default:\n                return \"未知\";\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"healthy\":\n                return \"bg-green-100 text-green-700\";\n            case \"warning\":\n                return \"bg-yellow-100 text-yellow-700\";\n            case \"error\":\n                return \"bg-red-100 text-red-700\";\n            default:\n                return \"bg-gray-100 text-gray-700\";\n        }\n    };\n    const getTypeIcon = (type)=>{\n        switch(type){\n            case \"kafka\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-8 h-8 text-white\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 28\n                }, this);\n            case \"rabbitmq\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-8 h-8 text-white\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                    lineNumber: 134,\n                    columnNumber: 31\n                }, this);\n            case \"redis\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-8 h-8 text-white\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                    lineNumber: 135,\n                    columnNumber: 28\n                }, this);\n            case \"database\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-8 h-8 text-white\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                    lineNumber: 136,\n                    columnNumber: 31\n                }, this);\n            case \"ftp\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-8 h-8 text-white\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                    lineNumber: 137,\n                    columnNumber: 26\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                    className: \"w-8 h-8 text-white\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getTypeColor = (type)=>{\n        switch(type){\n            case \"kafka\":\n                return \"from-blue-500 to-indigo-500\";\n            case \"rabbitmq\":\n                return \"from-indigo-500 to-cyan-500\";\n            case \"redis\":\n                return \"from-red-500 to-pink-500\";\n            case \"database\":\n                return \"from-green-500 to-emerald-500\";\n            case \"ftp\":\n                return \"from-blue-500 to-indigo-500\";\n            default:\n                return \"from-gray-500 to-slate-500\";\n        }\n    };\n    const overallStats = [\n        {\n            label: \"总通道数\",\n            value: \"5\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            color: \"blue\"\n        },\n        {\n            label: \"健康通道\",\n            value: \"3\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_1__[\"default\"],\n            color: \"green\"\n        },\n        {\n            label: \"总吞吐量\",\n            value: \"6.2K/s\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            color: \"indigo\"\n        },\n        {\n            label: \"平均可用性\",\n            value: \"99.5%\",\n            icon: _barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            color: \"cyan\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-gray-900 mb-4\",\n                        children: \"通道管理中心\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                        lineNumber: 164,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600\",\n                        children: \"管理各缓冲通道的运行情况、性能指标、部署架构和集群状态\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                lineNumber: 163,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: overallStats.map((stat)=>{\n                    const Icon = stat.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                            lineNumber: 179,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                            lineNumber: 180,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 15\n                        }, this)\n                    }, stat.label, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                        lineNumber: 173,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: channels.map((channel)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 backdrop-blur-sm rounded-xl shadow-md border border-white/20 hover:shadow-lg transition-all duration-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: `w-12 h-12 bg-gradient-to-br ${getTypeColor(channel.type)} rounded-xl flex items-center justify-center flex-shrink-0`,\n                                                children: getTypeIcon(channel.type)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                lineNumber: 202,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-bold text-gray-900\",\n                                                                children: channel.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                                lineNumber: 207,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(channel.status)}`,\n                                                                children: getStatusText(channel.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                                lineNumber: 208,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            getStatusIcon(channel.status)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                        lineNumber: 206,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-2\",\n                                                        children: channel.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                        lineNumber: 213,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 text-xs text-gray-500\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"节点: \",\n                                                                    channel.nodes\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                                lineNumber: 215,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"可用性: \",\n                                                                    channel.uptime\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                                lineNumber: 216,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"吞吐量: \",\n                                                                    channel.throughput\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \"存储: \",\n                                                                    channel.storage\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                        lineNumber: 214,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                lineNumber: 205,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50/50 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-600 flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                                    lineNumber: 229,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"CPU\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                                    lineNumber: 230,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium\",\n                                                            children: [\n                                                                channel.cpu,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                    lineNumber: 227,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-200 rounded-full h-1.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `h-1.5 rounded-full transition-all duration-500 ${channel.cpu > 80 ? \"bg-red-500\" : channel.cpu > 60 ? \"bg-yellow-500\" : \"bg-green-500\"}`,\n                                                        style: {\n                                                            width: `${channel.cpu}%`\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                        lineNumber: 235,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50/50 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-600 flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                                    lineNumber: 247,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"内存\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                                    lineNumber: 248,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium\",\n                                                            children: [\n                                                                channel.memory,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 250,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-200 rounded-full h-1.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `h-1.5 rounded-full transition-all duration-500 ${channel.memory > 80 ? \"bg-red-500\" : channel.memory > 60 ? \"bg-yellow-500\" : \"bg-green-500\"}`,\n                                                        style: {\n                                                            width: `${channel.memory}%`\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50/50 rounded-lg p-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-600 flex items-center space-x-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                    className: \"w-3 h-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                                    lineNumber: 265,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: \"网络\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                                    lineNumber: 266,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium\",\n                                                            children: [\n                                                                channel.network,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 268,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                    lineNumber: 263,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-200 rounded-full h-1.5\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: `h-1.5 rounded-full transition-all duration-500 ${channel.network > 80 ? \"bg-red-500\" : channel.network > 60 ? \"bg-yellow-500\" : \"bg-green-500\"}`,\n                                                        style: {\n                                                            width: `${channel.network}%`\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                        lineNumber: 271,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                    lineNumber: 270,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                            lineNumber: 262,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-6 text-xs text-gray-600\",\n                                            children: [\n                                                channel.type === \"kafka\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"分区: \",\n                                                                channel.partitions\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"消费者: \",\n                                                                channel.consumers\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 287,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true),\n                                                channel.type === \"rabbitmq\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"队列: \",\n                                                                channel.queues\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 292,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"交换器: \",\n                                                                channel.exchanges\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 293,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true),\n                                                channel.type === \"redis\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"键: \",\n                                                                channel.keys\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 298,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"命中率: \",\n                                                                channel.hitRate\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 299,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true),\n                                                channel.type === \"database\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"连接: \",\n                                                                channel.connections\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 304,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"表: \",\n                                                                channel.tables\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true),\n                                                channel.type === \"ftp\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"文件: \",\n                                                                channel.files\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \"传输: \",\n                                                                channel.transfers\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                            lineNumber: 283,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-blue-600 hover:text-blue-700 text-xs transition-colors flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 319,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"监控\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-600 hover:text-green-700 text-xs transition-colors flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Activity_AlertTriangle_BarChart3_CheckCircle_Clock_Cpu_Database_FileText_HardDrive_Layers_Network_Server_Settings_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 323,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"配置\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-blue-600 hover:text-blue-700 text-xs transition-colors\",\n                                                    children: \"详情\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                            lineNumber: 317,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, this)\n                    }, channel.id, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n                lineNumber: 192,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\ChannelManagement.tsx\",\n        lineNumber: 161,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/aggregation/components/ChannelManagement.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/aggregation/components/TaskManagement.tsx":
/*!***********************************************************!*\
  !*** ./src/app/aggregation/components/TaskManagement.tsx ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ TaskManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BarChart3,Building2,CheckCircle,Clock,Database,GitMerge,Pause,Play,Plus,Settings!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/git-merge.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BarChart3,Building2,CheckCircle,Clock,Database,GitMerge,Pause,Play,Plus,Settings!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BarChart3,Building2,CheckCircle,Clock,Database,GitMerge,Pause,Play,Plus,Settings!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BarChart3,Building2,CheckCircle,Clock,Database,GitMerge,Pause,Play,Plus,Settings!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BarChart3,Building2,CheckCircle,Clock,Database,GitMerge,Pause,Play,Plus,Settings!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BarChart3,Building2,CheckCircle,Clock,Database,GitMerge,Pause,Play,Plus,Settings!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BarChart3,Building2,CheckCircle,Clock,Database,GitMerge,Pause,Play,Plus,Settings!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BarChart3,Building2,CheckCircle,Clock,Database,GitMerge,Pause,Play,Plus,Settings!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BarChart3,Building2,CheckCircle,Clock,Database,GitMerge,Pause,Play,Plus,Settings!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BarChart3,Building2,CheckCircle,Clock,Database,GitMerge,Pause,Play,Plus,Settings!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/pause.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BarChart3,Building2,CheckCircle,Clock,Database,GitMerge,Pause,Play,Plus,Settings!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,ArrowRight,BarChart3,Building2,CheckCircle,Clock,Database,GitMerge,Pause,Play,Plus,Settings!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction TaskManagement() {\n    const aggregationTasks = [\n        {\n            id: 1,\n            name: \"人口数据汇聚\",\n            description: \"整合公安、民政、人社等部门人口相关数据\",\n            sources: [\n                \"公安局\",\n                \"民政局\",\n                \"人社局\"\n            ],\n            status: \"running\",\n            progress: 85,\n            lastRun: \"2分钟前\",\n            records: \"2.4M\",\n            schedule: \"每小时\"\n        },\n        {\n            id: 2,\n            name: \"企业信息汇聚\",\n            description: \"汇聚市场监管、税务、工信等部门企业数据\",\n            sources: [\n                \"市场监管局\",\n                \"税务局\",\n                \"工信局\"\n            ],\n            status: \"completed\",\n            progress: 100,\n            lastRun: \"1小时前\",\n            records: \"156K\",\n            schedule: \"每日\"\n        },\n        {\n            id: 3,\n            name: \"经济指标汇聚\",\n            description: \"统计、财政、发改等部门经济数据整合\",\n            sources: [\n                \"统计局\",\n                \"财政局\",\n                \"发改委\"\n            ],\n            status: \"error\",\n            progress: 45,\n            lastRun: \"3小时前\",\n            records: \"89K\",\n            schedule: \"每日\"\n        },\n        {\n            id: 4,\n            name: \"环境数据汇聚\",\n            description: \"环保、水务、应急等部门环境监测数据\",\n            sources: [\n                \"生态环境局\",\n                \"水务局\",\n                \"应急管理局\"\n            ],\n            status: \"scheduled\",\n            progress: 0,\n            lastRun: \"明天 09:00\",\n            records: \"234K\",\n            schedule: \"每周\"\n        }\n    ];\n    const quickStats = [\n        {\n            label: \"汇聚任务\",\n            value: \"8\",\n            trend: \"+2\",\n            icon: _barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            color: \"blue\"\n        },\n        {\n            label: \"数据源\",\n            value: \"24\",\n            trend: \"+3\",\n            icon: _barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            color: \"indigo\"\n        },\n        {\n            label: \"汇聚记录\",\n            value: \"3.2M\",\n            trend: \"+18%\",\n            icon: _barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            color: \"green\"\n        },\n        {\n            label: \"参与部门\",\n            value: \"15\",\n            trend: \"+1\",\n            icon: _barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            color: \"cyan\"\n        }\n    ];\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"running\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-5 h-5 text-blue-500 animate-spin\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 30\n                }, this);\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-5 h-5 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 32\n                }, this);\n            case \"error\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-5 h-5 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                    lineNumber: 79,\n                    columnNumber: 28\n                }, this);\n            case \"scheduled\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-5 h-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                    lineNumber: 80,\n                    columnNumber: 32\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-5 h-5 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"running\":\n                return \"运行中\";\n            case \"completed\":\n                return \"已完成\";\n            case \"error\":\n                return \"错误\";\n            case \"scheduled\":\n                return \"已计划\";\n            default:\n                return \"未知\";\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"running\":\n                return \"bg-blue-100 text-blue-700\";\n            case \"completed\":\n                return \"bg-green-100 text-green-700\";\n            case \"error\":\n                return \"bg-red-100 text-red-700\";\n            case \"scheduled\":\n                return \"bg-gray-100 text-gray-700\";\n            default:\n                return \"bg-gray-100 text-gray-700\";\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-gray-900 mb-4\",\n                        children: \"任务管理中心\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl text-gray-600\",\n                        children: \"管理和监控各采集Agent汇聚至缓冲通道的任务\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n                children: quickStats.map((stat, index)=>{\n                    const Icon = stat.icon;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm font-medium text-gray-600 mb-1\",\n                                            children: stat.label\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-3xl font-bold text-gray-900\",\n                                            children: stat.value\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-green-600 font-medium\",\n                                            children: stat.trend\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                            lineNumber: 126,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: `w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                        className: \"w-6 h-6 text-white\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 15\n                        }, this)\n                    }, stat.label, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 13\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                lineNumber: 114,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"bg-blue-600 text-white px-6 py-3 rounded-xl hover:bg-blue-700 transition-colors flex items-center space-x-2 font-medium\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                    lineNumber: 141,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"创建汇聚任务\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                    lineNumber: 142,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"bg-white/80 backdrop-blur-sm text-gray-700 px-6 py-3 rounded-xl hover:bg-white border border-gray-200 transition-all flex items-center space-x-2 font-medium\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-5 h-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"汇聚规则\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                            lineNumber: 144,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                    lineNumber: 139,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: aggregationTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-white/80 backdrop-blur-sm rounded-xl shadow-md border border-white/20 hover:shadow-lg transition-all duration-300\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-4 flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center flex-shrink-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                    lineNumber: 163,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 mb-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-lg font-bold text-gray-900\",\n                                                                children: task.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: `px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(task.status)}`,\n                                                                children: getStatusText(task.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                                lineNumber: 168,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            getStatusIcon(task.status)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600 mb-2\",\n                                                        children: task.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                        lineNumber: 173,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4 text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-500\",\n                                                                children: \"数据源:\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                                lineNumber: 175,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-wrap gap-1\",\n                                                                children: task.sources.map((source, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full text-xs\",\n                                                                        children: source\n                                                                    }, idx, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                                        lineNumber: 178,\n                                                                        columnNumber: 27\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                                lineNumber: 176,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                    lineNumber: 160,\n                                    columnNumber: 15\n                                }, this),\n                                task.status === \"running\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mb-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between text-xs text-gray-600 mb-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"汇聚进度\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        task.progress,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                            lineNumber: 191,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full bg-gray-200 rounded-full h-1.5\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gradient-to-r from-blue-500 to-indigo-500 h-1.5 rounded-full transition-all duration-500\",\n                                                style: {\n                                                    width: `${task.progress}%`\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                            lineNumber: 195,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-6 text-xs text-gray-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"记录: \",\n                                                        task.records\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"频率: \",\n                                                        task.schedule\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: [\n                                                        \"最后执行: \",\n                                                        task.lastRun\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-3\",\n                                            children: [\n                                                task.status === \"running\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-red-600 hover:text-red-700 text-xs transition-colors flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                            lineNumber: 216,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"暂停\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                            lineNumber: 217,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                    lineNumber: 215,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-green-600 hover:text-green-700 text-xs transition-colors flex items-center space-x-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"启动\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"text-blue-600 hover:text-blue-700 text-xs transition-colors\",\n                                                    children: \"日志\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    href: `/aggregation/task/${task.id}`,\n                                                    className: \"text-blue-600 hover:text-blue-700 flex items-center space-x-1 transition-colors text-xs\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"管理\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_ArrowRight_BarChart3_Building2_CheckCircle_Clock_Database_GitMerge_Pause_Play_Plus_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"w-3 h-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                            lineNumber: 233,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                                    lineNumber: 228,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                            lineNumber: 213,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this)\n                    }, task.id, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n                lineNumber: 152,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\components\\\\TaskManagement.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/aggregation/components/TaskManagement.tsx\n");

/***/ }),

/***/ "(ssr)/./src/app/aggregation/layout.tsx":
/*!****************************************!*\
  !*** ./src/app/aggregation/layout.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AggregationSystemLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_GitMerge_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,GitMerge,Settings!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/git-merge.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_GitMerge_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,GitMerge,Settings!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_GitMerge_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,GitMerge,Settings!=!lucide-react */ \"(ssr)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _components_TaskManagement__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./components/TaskManagement */ \"(ssr)/./src/app/aggregation/components/TaskManagement.tsx\");\n/* harmony import */ var _components_ChannelManagement__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./components/ChannelManagement */ \"(ssr)/./src/app/aggregation/components/ChannelManagement.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction AggregationSystemLayout() {\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"tasks\");\n    const renderContent = ()=>{\n        switch(activeTab){\n            case \"tasks\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TaskManagement__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 16\n                }, this);\n            case \"channels\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ChannelManagement__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                    lineNumber: 16,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_TaskManagement__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                    lineNumber: 18,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto px-8 py-4\",\n                    style: {\n                        maxWidth: \"calc(100vw - 4rem)\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center shadow-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_GitMerge_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"w-6 h-6 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                                                    lineNumber: 32,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                                                lineNumber: 31,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                                        children: \"\\uD83D\\uDD17 数据汇聚系统\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                                                        lineNumber: 35,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: \"跨系统数据整合汇聚平台\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                                                        lineNumber: 38,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                                                lineNumber: 34,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-6 ml-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"tasks\"),\n                                                className: `py-2 px-4 rounded-lg font-medium text-sm transition-colors flex items-center space-x-2 ${activeTab === \"tasks\" ? \"bg-blue-100 text-blue-600 border border-blue-200\" : \"text-gray-500 hover:text-gray-700 hover:bg-gray-100\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_GitMerge_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                                                        lineNumber: 52,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"任务管理\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                                                        lineNumber: 53,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                                                lineNumber: 44,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveTab(\"channels\"),\n                                                className: `py-2 px-4 rounded-lg font-medium text-sm transition-colors flex items-center space-x-2 ${activeTab === \"channels\" ? \"bg-blue-100 text-blue-600 border border-blue-200\" : \"text-gray-500 hover:text-gray-700 hover:bg-gray-100\"}`,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_GitMerge_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                                                        lineNumber: 63,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"通道管理\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                                                        lineNumber: 64,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                                                lineNumber: 55,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                                        lineNumber: 43,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        className: \"text-gray-600 hover:text-blue-600 transition-colors text-sm font-medium\",\n                                        children: \"返回首页\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/dashboard\",\n                                        className: \"bg-blue-600 text-white px-4 py-2 rounded-xl hover:bg-blue-700 transition-colors text-sm font-medium\",\n                                        children: \"主控台\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                        lineNumber: 27,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                lineNumber: 25,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"relative z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mx-auto px-8 py-8\",\n                    style: {\n                        maxWidth: \"calc(100vw - 4rem)\"\n                    },\n                    children: renderContent()\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 pointer-events-none overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-float\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-indigo-400/20 to-blue-400/20 rounded-full blur-3xl animate-float\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n                lineNumber: 96,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\aggregation\\\\layout.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/aggregation/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"0d5fe442f342\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHl1bnl1L21haW4vLi9zcmMvYXBwL2dsb2JhbHMuY3NzP2U0MmYiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwZDVmZTQ0MmYzNDJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/aggregation/layout.tsx":
/*!****************************************!*\
  !*** ./src/app/aggregation/layout.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Workspace\yy-zs\web\apps\main\src\app\aggregation\layout.tsx#default`));


/***/ }),

/***/ "(rsc)/./src/app/aggregation/page.tsx":
/*!**************************************!*\
  !*** ./src/app/aggregation/page.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AggregationSystemPage)\n/* harmony export */ });\nfunction AggregationSystemPage() {\n    // 这个页面现在由 layout.tsx 处理，显示任务管理组件\n    return null;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FnZ3JlZ2F0aW9uL3BhZ2UudHN4IiwibWFwcGluZ3MiOiI7Ozs7QUFBZSxTQUFTQTtJQUN0QixpQ0FBaUM7SUFDakMsT0FBTztBQUNUIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vQHl1bnl1L21haW4vLi9zcmMvYXBwL2FnZ3JlZ2F0aW9uL3BhZ2UudHN4P2VhOGMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQWdncmVnYXRpb25TeXN0ZW1QYWdlKCkge1xuICAvLyDov5nkuKrpobXpnaLnjrDlnKjnlLEgbGF5b3V0LnRzeCDlpITnkIbvvIzmmL7npLrku7vliqHnrqHnkIbnu4Tku7ZcbiAgcmV0dXJuIG51bGxcbn1cbiJdLCJuYW1lcyI6WyJBZ2dyZWdhdGlvblN5c3RlbVBhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/app/aggregation/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n\n\nconst metadata = {\n    title: \"云宇政数平台\",\n    description: \"统一的政数局数据管理平台 - 大屏展示、报表分析、数据采集、数据汇聚、数据清洗治理、数据资源池管理、设备监控\",\n    keywords: \"政数局,数据平台,大屏,报表,数据采集,数据治理,设备监控\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"zh-CN\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans\",\n            children: children\n        }, void 0, false, {\n            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ3NCO0FBRWYsTUFBTUEsV0FBcUI7SUFDaENDLE9BQU87SUFDUEMsYUFBYTtJQUNiQyxVQUFVO0FBQ1osRUFBQztBQUVjLFNBQVNDLFdBQVcsRUFDakNDLFFBQVEsRUFHVDtJQUNDLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFVO3NCQUNiSjs7Ozs7Ozs7Ozs7QUFJVCIsInNvdXJjZXMiOlsid2VicGFjazovL0B5dW55dS9tYWluLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHR5cGUgeyBNZXRhZGF0YSB9IGZyb20gJ25leHQnXG5pbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAn5LqR5a6H5pS/5pWw5bmz5Y+wJyxcbiAgZGVzY3JpcHRpb246ICfnu5/kuIDnmoTmlL/mlbDlsYDmlbDmja7nrqHnkIblubPlj7AgLSDlpKflsY/lsZXnpLrjgIHmiqXooajliIbmnpDjgIHmlbDmja7ph4fpm4bjgIHmlbDmja7msYfogZrjgIHmlbDmja7muIXmtJfmsrvnkIbjgIHmlbDmja7otYTmupDmsaDnrqHnkIbjgIHorr7lpIfnm5HmjqcnLFxuICBrZXl3b3JkczogJ+aUv+aVsOWxgCzmlbDmja7lubPlj7As5aSn5bGPLOaKpeihqCzmlbDmja7ph4fpm4Ys5pWw5o2u5rK755CGLOiuvuWkh+ebkeaOpycsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJ6aC1DTlwiPlxuICAgICAgPGJvZHkgY2xhc3NOYW1lPVwiZm9udC1zYW5zXCI+XG4gICAgICAgIHtjaGlsZHJlbn1cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJtZXRhZGF0YSIsInRpdGxlIiwiZGVzY3JpcHRpb24iLCJrZXl3b3JkcyIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSIsImNsYXNzTmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1","vendor-chunks/@swc+helpers@0.5.5","vendor-chunks/lucide-react@0.294.0_react@18.3.1"], () => (__webpack_exec__("(rsc)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Faggregation%2Fpage&page=%2Faggregation%2Fpage&appPaths=%2Faggregation%2Fpage&pagePath=private-next-app-dir%2Faggregation%2Fpage.tsx&appDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CWorkspace%5Cyy-zs%5Cweb%5Capps%5Cmain&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
'use client'

import Link from 'next/link'
import { 
  ArrowLeft,
  Network,
  Wifi,
  Router,
  Globe,
  Activity,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Download,
  Upload,
  Zap,
  Shield,
  Eye,
  Settings,
  RefreshCw,
  BarChart3,
  PieChart,
  Monitor
} from 'lucide-react'

export default function NetworkMonitoringPage() {
  // 网络概览统计
  const networkStats = [
    { 
      label: '网络设备', 
      value: '24', 
      online: '22',
      icon: Router, 
      color: 'blue',
      status: 'normal'
    },
    { 
      label: '总带宽', 
      value: '10Gbps', 
      usage: '3.2Gbps',
      icon: Wifi, 
      color: 'green',
      status: 'normal'
    },
    { 
      label: '平均延迟', 
      value: '12ms', 
      change: '-2ms',
      icon: Activity, 
      color: 'purple',
      status: 'good'
    },
    { 
      label: '丢包率', 
      value: '0.02%', 
      change: '-0.01%',
      icon: AlertTriangle, 
      color: 'orange',
      status: 'normal'
    }
  ]

  // 网络设备列表
  const networkDevices = [
    {
      id: 1,
      name: '核心交换机-01',
      type: '核心交换机',
      ip: '***********',
      location: '机房A',
      status: 'online',
      uptime: '45天 12小时',
      ports: { total: 48, used: 36, available: 12 },
      traffic: { in: 2.5, out: 1.8 },
      cpu: 25,
      memory: 45,
      temperature: 42
    },
    {
      id: 2,
      name: '汇聚交换机-01',
      type: '汇聚交换机',
      ip: '***********',
      location: '机房A',
      status: 'online',
      uptime: '32天 8小时',
      ports: { total: 24, used: 18, available: 6 },
      traffic: { in: 1.2, out: 0.9 },
      cpu: 18,
      memory: 32,
      temperature: 38
    },
    {
      id: 3,
      name: '接入交换机-01',
      type: '接入交换机',
      ip: '***********0',
      location: '机房B',
      status: 'warning',
      uptime: '15天 6小时',
      ports: { total: 24, used: 22, available: 2 },
      traffic: { in: 0.8, out: 0.6 },
      cpu: 65,
      memory: 78,
      temperature: 55
    },
    {
      id: 4,
      name: '防火墙-01',
      type: '防火墙',
      ip: '***********54',
      location: '机房A',
      status: 'online',
      uptime: '28天 14小时',
      ports: { total: 8, used: 6, available: 2 },
      traffic: { in: 3.1, out: 2.8 },
      cpu: 42,
      memory: 58,
      temperature: 48
    },
    {
      id: 5,
      name: '路由器-01',
      type: '路由器',
      ip: '********',
      location: '机房A',
      status: 'offline',
      uptime: '0天 0小时',
      ports: { total: 4, used: 0, available: 4 },
      traffic: { in: 0, out: 0 },
      cpu: 0,
      memory: 0,
      temperature: 0
    }
  ]

  // 网络流量统计
  const trafficStats = [
    { time: '00:00', inbound: 1.2, outbound: 0.8 },
    { time: '04:00', inbound: 0.8, outbound: 0.6 },
    { time: '08:00', inbound: 2.5, outbound: 1.9 },
    { time: '12:00', inbound: 3.2, outbound: 2.4 },
    { time: '16:00', inbound: 2.8, outbound: 2.1 },
    { time: '20:00', inbound: 2.1, outbound: 1.6 }
  ]

  // 网络连接状态
  const connectionStatus = [
    { source: '核心交换机-01', target: '汇聚交换机-01', status: 'active', bandwidth: '10Gbps', utilization: 32 },
    { source: '核心交换机-01', target: '汇聚交换机-02', status: 'active', bandwidth: '10Gbps', utilization: 28 },
    { source: '汇聚交换机-01', target: '接入交换机-01', status: 'active', bandwidth: '1Gbps', utilization: 65 },
    { source: '汇聚交换机-01', target: '接入交换机-02', status: 'warning', bandwidth: '1Gbps', utilization: 85 },
    { source: '核心交换机-01', target: '防火墙-01', status: 'active', bandwidth: '10Gbps', utilization: 45 },
    { source: '防火墙-01', target: '路由器-01', status: 'error', bandwidth: '1Gbps', utilization: 0 }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
      case 'active': return 'text-green-600 bg-green-100'
      case 'offline':
      case 'error': return 'text-red-600 bg-red-100'
      case 'warning': return 'text-orange-600 bg-orange-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getUtilizationColor = (utilization: number) => {
    if (utilization >= 80) return 'bg-red-500'
    if (utilization >= 60) return 'bg-orange-500'
    return 'bg-green-500'
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/dashboard/monitoring" className="p-2 hover:bg-gray-100 rounded-lg transition-colors">
            <ArrowLeft className="w-5 h-5 text-gray-600" />
          </Link>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center space-x-3">
              <Network className="w-8 h-8 text-indigo-600" />
              <span>网络监控</span>
            </h1>
            <p className="mt-1 text-gray-600">网络拓扑监控，网络流量分析和网络设备状态监控</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2 bg-green-50 px-3 py-2 rounded-lg">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-green-700">实时监控中</span>
          </div>
          <button className="btn-secondary flex items-center space-x-2">
            <RefreshCw className="w-4 h-4" />
            <span>刷新</span>
          </button>
          <button className="btn-secondary flex items-center space-x-2">
            <Settings className="w-4 h-4" />
            <span>网络设置</span>
          </button>
        </div>
      </div>

      {/* 网络概览统计 */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">网络概览</h2>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Clock className="w-4 h-4" />
            <span>最后更新: 2024-01-15 14:30</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {networkStats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <div key={stat.label} className="card">
                <div className="card-content">
                  <div className="flex items-center justify-between mb-4">
                    <div className={`w-12 h-12 rounded-2xl flex items-center justify-center ${
                      stat.color === 'blue' ? 'bg-blue-100' :
                      stat.color === 'green' ? 'bg-green-100' :
                      stat.color === 'purple' ? 'bg-purple-100' : 'bg-orange-100'
                    }`}>
                      <Icon className={`w-6 h-6 ${
                        stat.color === 'blue' ? 'text-blue-600' :
                        stat.color === 'green' ? 'text-green-600' :
                        stat.color === 'purple' ? 'text-purple-600' : 'text-orange-600'
                      }`} />
                    </div>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      stat.status === 'good' ? 'bg-green-100 text-green-700' :
                      stat.status === 'normal' ? 'bg-blue-100 text-blue-700' : 'bg-orange-100 text-orange-700'
                    }`}>
                      {stat.status === 'good' ? '优秀' :
                       stat.status === 'normal' ? '正常' : '警告'}
                    </span>
                  </div>
                  
                  <div className="text-sm font-medium text-gray-600 mb-1">{stat.label}</div>
                  <div className="text-2xl font-bold text-gray-900 mb-2">{stat.value}</div>
                  
                  {stat.online && (
                    <div className="text-xs text-gray-500">在线: {stat.online}/{stat.value}</div>
                  )}
                  {stat.usage && (
                    <div className="text-xs text-gray-500">使用: {stat.usage}</div>
                  )}
                  {stat.change && (
                    <div className="flex items-center space-x-1 text-xs">
                      {stat.change.startsWith('-') ? (
                        <TrendingDown className="w-3 h-3 text-green-500" />
                      ) : (
                        <TrendingUp className="w-3 h-3 text-red-500" />
                      )}
                      <span className={stat.change.startsWith('-') ? 'text-green-600' : 'text-red-600'}>
                        {stat.change}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      </section>

      {/* 网络拓扑图 */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">网络拓扑</h2>
          <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
            查看完整拓扑 →
          </button>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="h-80 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl flex items-center justify-center">
              <div className="text-center text-gray-500">
                <Monitor className="w-16 h-16 mx-auto mb-4" />
                <h3 className="text-lg font-medium mb-2">网络拓扑图</h3>
                <p>交互式网络拓扑图将在此处显示</p>
                <p className="text-sm mt-2">显示设备连接关系、流量状态和告警信息</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 网络设备状态 */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-bold text-gray-900">网络设备状态</h2>
          <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
            查看所有设备 →
          </button>
        </div>

        <div className="card">
          <div className="card-content">
            <div className="space-y-6">
              {networkDevices.map((device) => (
                <div key={device.id} className="border border-gray-100 rounded-xl p-6 hover:bg-gray-50/50 transition-colors">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-start space-x-4">
                      <div className={`w-12 h-12 rounded-2xl flex items-center justify-center ${
                        device.status === 'online' ? 'bg-green-100' :
                        device.status === 'offline' ? 'bg-red-100' : 'bg-orange-100'
                      }`}>
                        <Router className={`w-6 h-6 ${
                          device.status === 'online' ? 'text-green-600' :
                          device.status === 'offline' ? 'text-red-600' : 'text-orange-600'
                        }`} />
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900">{device.name}</h4>
                        <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                          <span>{device.type}</span>
                          <span>{device.ip}</span>
                          <span>{device.location}</span>
                          <span>运行时间: {device.uptime}</span>
                        </div>
                      </div>
                    </div>
                    
                    <span className={`px-3 py-1 text-xs font-medium rounded-full ${getStatusColor(device.status)}`}>
                      {device.status === 'online' ? '在线' :
                       device.status === 'offline' ? '离线' : '警告'}
                    </span>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                    {/* 端口使用情况 */}
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">端口使用</h5>
                      <div className="text-lg font-bold text-gray-900 mb-1">
                        {device.ports.used}/{device.ports.total}
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full"
                          style={{ width: `${(device.ports.used / device.ports.total) * 100}%` }}
                        ></div>
                      </div>
                      <div className="text-xs text-gray-500">可用: {device.ports.available}</div>
                    </div>

                    {/* 流量统计 */}
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">流量统计</h5>
                      <div className="space-y-1 text-sm">
                        <div className="flex items-center space-x-2">
                          <Download className="w-3 h-3 text-green-600" />
                          <span>入: {device.traffic.in}Gbps</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Upload className="w-3 h-3 text-blue-600" />
                          <span>出: {device.traffic.out}Gbps</span>
                        </div>
                      </div>
                    </div>

                    {/* 性能指标 */}
                    <div>
                      <h5 className="text-sm font-medium text-gray-700 mb-2">性能指标</h5>
                      <div className="space-y-1 text-sm">
                        <div>CPU: {device.cpu}%</div>
                        <div>内存: {device.memory}%</div>
                        <div>温度: {device.temperature}°C</div>
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex items-center justify-end space-x-2">
                      <button className="p-2 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                        <Settings className="w-4 h-4" />
                      </button>
                      <button className="p-2 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded-lg transition-colors">
                        <RefreshCw className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* 网络连接状态 */}
      <section className="space-y-6">
        <h2 className="text-xl font-bold text-gray-900">网络连接状态</h2>

        <div className="card">
          <div className="card-content">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">源设备</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">目标设备</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">带宽</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">利用率</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">状态</th>
                  </tr>
                </thead>
                <tbody>
                  {connectionStatus.map((connection, index) => (
                    <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4 font-medium text-gray-900">{connection.source}</td>
                      <td className="py-3 px-4 text-gray-600">{connection.target}</td>
                      <td className="py-3 px-4 text-gray-600">{connection.bandwidth}</td>
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-20 bg-gray-200 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${getUtilizationColor(connection.utilization)}`}
                              style={{ width: `${connection.utilization}%` }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-600">{connection.utilization}%</span>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(connection.status)}`}>
                          {connection.status === 'active' ? '正常' :
                           connection.status === 'warning' ? '警告' : '异常'}
                        </span>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* 流量趋势图 */}
      <section className="space-y-6">
        <h2 className="text-xl font-bold text-gray-900">流量趋势</h2>
        
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
              <BarChart3 className="w-5 h-5 text-blue-600" />
              <span>24小时流量统计</span>
            </h3>
          </div>
          <div className="card-content">
            <div className="h-64 bg-gray-50 rounded-xl flex items-center justify-center">
              <div className="text-center text-gray-500">
                <BarChart3 className="w-12 h-12 mx-auto mb-2" />
                <p>流量趋势图表将在此处显示</p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

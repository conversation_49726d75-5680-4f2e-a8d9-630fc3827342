'use client'

import Link from 'next/link'
import {
  Server,
  Monitor,
  Activity,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  Cpu,
  HardDrive,
  MemoryStick,
  Network,
  Zap,
  TrendingUp,
  TrendingDown,
  Eye,
  Settings,
  BarChart3,
  PieChart,
  LineChart,
  Calendar,
  Filter,
  Download,
  RefreshCw,
  Plus,
  ArrowRight,
  Gauge,
  Shield,
  Wifi,
  Database,
  Cloud,
  Target
} from 'lucide-react'

export default function MonitoringPage() {
  // 设备台账统计
  const deviceStats = [
    {
      label: '设备总数',
      value: '156',
      change: '+12',
      changeType: 'increase',
      icon: Server,
      color: 'blue',
      description: '本月新增12台设备'
    },
    {
      label: '在线设备',
      value: '142',
      total: '156',
      percentage: '91%',
      icon: CheckCircle,
      color: 'green',
      description: '14台设备离线'
    },
    {
      label: '告警设备',
      value: '8',
      change: '-3',
      changeType: 'decrease',
      icon: AlertTriangle,
      color: 'orange',
      description: '较昨日减少3台'
    },
    {
      label: '异常设备',
      value: '2',
      change: '+1',
      changeType: 'increase',
      icon: XCircle,
      color: 'red',
      description: '需要立即处理'
    }
  ]

  // 性能指标概览
  const performanceMetrics = [
    {
      label: 'CPU平均使用率',
      value: '68',
      unit: '%',
      trend: 'up',
      status: 'warning',
      target: '< 80%'
    },
    {
      label: '内存平均使用率',
      value: '72',
      unit: '%',
      trend: 'up',
      status: 'warning',
      target: '< 85%'
    },
    {
      label: '磁盘平均使用率',
      value: '45',
      unit: '%',
      trend: 'stable',
      status: 'normal',
      target: '< 90%'
    },
    {
      label: '网络平均延迟',
      value: '12',
      unit: 'ms',
      trend: 'down',
      status: 'good',
      target: '< 50ms'
    }
  ]

  // 告警统计
  const alertStats = [
    { type: '严重', count: 2, color: 'red', percentage: 15 },
    { type: '警告', count: 8, color: 'orange', percentage: 62 },
    { type: '信息', count: 3, color: 'blue', percentage: 23 }
  ]

  // 设备类型分布
  const deviceTypes = [
    { type: 'Web服务器', count: 45, percentage: 29, color: 'blue' },
    { type: '数据库服务器', count: 32, percentage: 21, color: 'green' },
    { type: '应用服务器', count: 38, percentage: 24, color: 'purple' },
    { type: '文件服务器', count: 25, percentage: 16, color: 'orange' },
    { type: '其他设备', count: 16, percentage: 10, color: 'gray' }
  ]

  // 监控子功能模块
  const monitoringModules = [
    {
      name: '实时监控',
      description: '设备实时状态监控，CPU、内存、磁盘等指标实时展示',
      href: '/monitoring/realtime',
      icon: Activity,
      gradient: 'from-blue-500 to-cyan-500',
      features: ['实时数据', '性能图表', '状态告警']
    },
    {
      name: '性能分析',
      description: '设备性能趋势分析，历史数据对比和性能优化建议',
      href: '/monitoring/performance',
      icon: TrendingUp,
      gradient: 'from-green-500 to-emerald-500',
      features: ['趋势分析', '性能报告', '优化建议']
    },
    {
      name: '告警管理',
      description: '告警规则配置，告警历史查询和告警处理流程管理',
      href: '/monitoring/alerts',
      icon: AlertTriangle,
      gradient: 'from-orange-500 to-red-500',
      features: ['告警规则', '历史记录', '处理流程']
    },
    {
      name: '设备管理',
      description: '设备台账管理，设备信息维护和设备生命周期管理',
      href: '/monitoring/devices',
      icon: Server,
      gradient: 'from-purple-500 to-violet-500',
      features: ['设备台账', '信息维护', '生命周期']
    },
    {
      name: '网络监控',
      description: '网络拓扑监控，网络流量分析和网络设备状态监控',
      href: '/monitoring/network',
      icon: Network,
      gradient: 'from-indigo-500 to-blue-500',
      features: ['网络拓扑', '流量分析', '连接状态']
    },
    {
      name: '日志分析',
      description: '系统日志收集分析，异常日志检测和日志统计报表',
      href: '/monitoring/logs',
      icon: Database,
      gradient: 'from-teal-500 to-cyan-500',
      features: ['日志收集', '异常检测', '统计报表']
    }
  ]

  return (
    <div className="w-full px-4 py-8 space-y-8">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
            设备监控总览
          </h1>
          <p className="mt-2 text-lg text-gray-600">
            全面监控各类设备运行状态，实时掌握系统性能和健康状况
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <button className="btn-secondary flex items-center space-x-2">
            <RefreshCw className="w-4 h-4" />
            <span>刷新数据</span>
          </button>
          <button className="btn-primary flex items-center space-x-2">
            <Plus className="w-4 h-4" />
            <span>添加设备</span>
          </button>
        </div>
      </div>

      {/* 设备台账统计 */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">设备台账概览</h2>
          <div className="flex items-center space-x-2 text-sm text-gray-500">
            <Clock className="w-4 h-4" />
            <span>最后更新: 2024-01-15 14:30</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {deviceStats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <div
                key={stat.label}
                className="card group hover:shadow-xl transition-all duration-500"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="card-content">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        <div className={`w-12 h-12 rounded-2xl flex items-center justify-center ${
                          stat.color === 'blue' ? 'bg-blue-100' :
                          stat.color === 'green' ? 'bg-green-100' :
                          stat.color === 'orange' ? 'bg-orange-100' : 'bg-red-100'
                        }`}>
                          <Icon className={`w-6 h-6 ${
                            stat.color === 'blue' ? 'text-blue-600' :
                            stat.color === 'green' ? 'text-green-600' :
                            stat.color === 'orange' ? 'text-orange-600' : 'text-red-600'
                          }`} />
                        </div>
                        <div>
                          <div className="text-sm font-medium text-gray-600">{stat.label}</div>
                          <div className="flex items-baseline space-x-2">
                            <span className="text-3xl font-bold text-gray-900">{stat.value}</span>
                            {stat.total && (
                              <span className="text-lg text-gray-500">/{stat.total}</span>
                            )}
                            {stat.percentage && (
                              <span className={`text-sm font-medium ${
                                stat.color === 'green' ? 'text-green-600' : 'text-gray-500'
                              }`}>
                                ({stat.percentage})
                              </span>
                            )}
                          </div>
                        </div>
                      </div>

                      {stat.change && (
                        <div className="flex items-center space-x-2">
                          {stat.changeType === 'increase' ? (
                            <TrendingUp className={`w-4 h-4 ${
                              stat.color === 'red' ? 'text-red-500' : 'text-green-500'
                            }`} />
                          ) : (
                            <TrendingDown className="w-4 h-4 text-green-500" />
                          )}
                          <span className={`text-sm font-medium ${
                            stat.changeType === 'increase' && stat.color === 'red' ? 'text-red-600' : 'text-green-600'
                          }`}>
                            {stat.change}
                          </span>
                        </div>
                      )}

                      <p className="text-xs text-gray-500 mt-2">{stat.description}</p>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </section>

      {/* 实时监控情况 */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">实时监控情况</h2>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-600">实时更新中</span>
            </div>
            <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
              查看详细监控 →
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {performanceMetrics.map((metric, index) => (
            <div
              key={metric.label}
              className="card group hover:shadow-xl transition-all duration-500"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="card-content">
                <div className="flex items-center justify-between mb-4">
                  <div className="text-sm font-medium text-gray-600">{metric.label}</div>
                  <div className="flex items-center space-x-1">
                    {metric.trend === 'up' && <TrendingUp className="w-4 h-4 text-red-500" />}
                    {metric.trend === 'down' && <TrendingDown className="w-4 h-4 text-green-500" />}
                    {metric.trend === 'stable' && <div className="w-4 h-1 bg-gray-400 rounded"></div>}
                  </div>
                </div>

                <div className="flex items-baseline space-x-2 mb-3">
                  <span className="text-3xl font-bold text-gray-900">{metric.value}</span>
                  <span className="text-lg text-gray-500">{metric.unit}</span>
                </div>

                <div className="flex items-center justify-between text-xs">
                  <span className="text-gray-500">目标: {metric.target}</span>
                  <span className={`px-2 py-1 rounded-full font-medium ${
                    metric.status === 'good' ? 'bg-green-100 text-green-700' :
                    metric.status === 'normal' ? 'bg-blue-100 text-blue-700' :
                    'bg-orange-100 text-orange-700'
                  }`}>
                    {metric.status === 'good' ? '良好' :
                     metric.status === 'normal' ? '正常' : '警告'}
                  </span>
                </div>

                {/* 简单的进度条 */}
                <div className="mt-4">
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-1000 ${
                        metric.status === 'good' ? 'bg-green-500' :
                        metric.status === 'normal' ? 'bg-blue-500' : 'bg-orange-500'
                      }`}
                      style={{ width: `${metric.value}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* 告警概览和设备分析 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 告警概览 */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-900">告警概览</h3>
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">今日</span>
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                  <span className="text-sm font-medium text-red-600">13条告警</span>
                </div>
              </div>
            </div>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              {alertStats.map((alert, index) => (
                <div key={alert.type} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded-full ${
                      alert.color === 'red' ? 'bg-red-500' :
                      alert.color === 'orange' ? 'bg-orange-500' : 'bg-blue-500'
                    }`}></div>
                    <span className="text-sm font-medium text-gray-900">{alert.type}告警</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span className="text-lg font-bold text-gray-900">{alert.count}</span>
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          alert.color === 'red' ? 'bg-red-500' :
                          alert.color === 'orange' ? 'bg-orange-500' : 'bg-blue-500'
                        }`}
                        style={{ width: `${alert.percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-500 w-8">{alert.percentage}%</span>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 pt-4 border-t border-gray-100">
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">处理率</span>
                <span className="font-medium text-green-600">85%</span>
              </div>
              <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                <div className="bg-green-500 h-2 rounded-full" style={{ width: '85%' }}></div>
              </div>
            </div>
          </div>
        </div>

        {/* 设备类型分布 */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center justify-between">
              <h3 className="text-xl font-bold text-gray-900">设备类型分布</h3>
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                查看详情 →
              </button>
            </div>
          </div>
          <div className="card-content">
            <div className="space-y-4">
              {deviceTypes.map((device, index) => (
                <div key={device.type} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded-full ${
                      device.color === 'blue' ? 'bg-blue-500' :
                      device.color === 'green' ? 'bg-green-500' :
                      device.color === 'purple' ? 'bg-purple-500' :
                      device.color === 'orange' ? 'bg-orange-500' : 'bg-gray-500'
                    }`}></div>
                    <span className="text-sm font-medium text-gray-900">{device.type}</span>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span className="text-lg font-bold text-gray-900">{device.count}</span>
                    <div className="w-20 bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          device.color === 'blue' ? 'bg-blue-500' :
                          device.color === 'green' ? 'bg-green-500' :
                          device.color === 'purple' ? 'bg-purple-500' :
                          device.color === 'orange' ? 'bg-orange-500' : 'bg-gray-500'
                        }`}
                        style={{ width: `${device.percentage}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-gray-500 w-8">{device.percentage}%</span>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 pt-4 border-t border-gray-100">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">156</div>
                <div className="text-sm text-gray-600">设备总数</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 时间维度概览 */}
      <section className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">时间维度概览</h2>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 bg-gray-100 rounded-lg p-1">
              <button className="px-3 py-1 text-sm font-medium bg-white text-gray-900 rounded shadow-sm">今天</button>
              <button className="px-3 py-1 text-sm font-medium text-gray-600 hover:text-gray-900">近7天</button>
              <button className="px-3 py-1 text-sm font-medium text-gray-600 hover:text-gray-900">近30天</button>
            </div>
            <button className="btn-secondary flex items-center space-x-2">
              <Calendar className="w-4 h-4" />
              <span>自定义时间</span>
            </button>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* 今日概览 */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                <Calendar className="w-5 h-5 text-blue-600" />
                <span>今日概览</span>
              </h3>
            </div>
            <div className="card-content">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">新增告警</span>
                  <span className="text-lg font-bold text-red-600">13</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">处理告警</span>
                  <span className="text-lg font-bold text-green-600">11</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">设备重启</span>
                  <span className="text-lg font-bold text-orange-600">3</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">平均响应时间</span>
                  <span className="text-lg font-bold text-blue-600">12ms</span>
                </div>
              </div>
            </div>
          </div>

          {/* 近期趋势 */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-green-600" />
                <span>近期趋势</span>
              </h3>
            </div>
            <div className="card-content">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">设备可用性</span>
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-4 h-4 text-green-500" />
                    <span className="text-lg font-bold text-green-600">+2.1%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">平均负载</span>
                  <div className="flex items-center space-x-2">
                    <TrendingDown className="w-4 h-4 text-green-500" />
                    <span className="text-lg font-bold text-green-600">-5.3%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">告警频率</span>
                  <div className="flex items-center space-x-2">
                    <TrendingDown className="w-4 h-4 text-green-500" />
                    <span className="text-lg font-bold text-green-600">-12%</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">处理效率</span>
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="w-4 h-4 text-green-500" />
                    <span className="text-lg font-bold text-green-600">+8.7%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 性能指标 */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                <Gauge className="w-5 h-5 text-purple-600" />
                <span>性能指标</span>
              </h3>
            </div>
            <div className="card-content">
              <div className="space-y-4">
                <div className="text-center">
                  <div className="text-3xl font-bold text-gray-900 mb-1">99.2%</div>
                  <div className="text-sm text-gray-600">系统可用性</div>
                  <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '99.2%' }}></div>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-100">
                  <div className="text-center">
                    <div className="text-xl font-bold text-blue-600">142</div>
                    <div className="text-xs text-gray-600">在线设备</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xl font-bold text-orange-600">8</div>
                    <div className="text-xs text-gray-600">告警设备</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 监控子功能模块入口 - 显眼的入口区域 */}
      <section className="space-y-8">
        <div className="text-center">
          <div className="inline-flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-indigo-50 px-8 py-4 rounded-2xl mb-6">
            <Target className="w-8 h-8 text-blue-600" />
            <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              设备监控功能模块
            </span>
          </div>
          <h2 className="text-4xl font-bold text-gray-900 mb-4">选择您需要的监控功能</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            点击下方功能模块，进入对应的设备监控子功能页面，获得更详细的监控数据和管理功能
          </p>
        </div>

        {/* 突出显示的功能模块网格 */}
        <div className="bg-gradient-to-r from-blue-500/10 to-indigo-500/10 rounded-3xl p-8 border-2 border-blue-200/50 shadow-2xl">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {monitoringModules.map((module, index) => {
              const Icon = module.icon
              return (
                <Link key={module.name} href={module.href}>
                  <div
                    className="bg-white/90 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 cursor-pointer group transform hover:scale-105 border border-white/50"
                    style={{ animationDelay: `${index * 100}ms` }}
                  >
                    <div className="relative overflow-hidden text-center">
                      {/* 背景渐变效果 */}
                      <div className={`absolute inset-0 bg-gradient-to-br ${module.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-3xl`}></div>

                      <div className="relative z-10">
                        <div className={`w-20 h-20 bg-gradient-to-br ${module.gradient} rounded-3xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110`}>
                          <Icon className="w-10 h-10 text-white" />
                        </div>

                        <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-700 transition-colors">
                          {module.name}
                        </h3>

                        <p className="text-gray-600 text-sm mb-6 leading-relaxed">
                          {module.description}
                        </p>

                        <div className="space-y-2 mb-6">
                          {module.features.map((feature, idx) => (
                            <div key={idx} className="flex items-center justify-center text-xs text-gray-500">
                              <CheckCircle className="w-3 h-3 text-green-500 mr-2" />
                              {feature}
                            </div>
                          ))}
                        </div>

                        <div className="flex items-center justify-center">
                          <span className="text-lg font-bold text-blue-600 group-hover:text-blue-700 transition-colors flex items-center">
                            进入模块
                            <ArrowRight className="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform" />
                          </span>
                        </div>
                      </div>

                      {/* 悬浮时的光效 */}
                      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                        <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white/50 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
                      </div>
                    </div>
                  </div>
                </Link>
              )
            })}
          </div>

          {/* 底部提示 */}
          <div className="text-center mt-12">
            <div className="inline-flex items-center space-x-3 text-gray-600">
              <Shield className="w-6 h-6" />
              <span className="text-lg">全方位设备监控，确保系统稳定运行</span>
            </div>
          </div>
        </div>
      </section>

      {/* 快速操作区域 */}
      <section className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-3xl p-8">
        <div className="text-center mb-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-4">快速操作</h3>
          <p className="text-gray-600">常用的监控管理操作，一键直达</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[
            { name: '导出报表', icon: Download, color: 'blue', description: '导出监控数据报表' },
            { name: '配置告警', icon: Settings, color: 'orange', description: '设置告警规则和阈值' },
            { name: '系统诊断', icon: Activity, color: 'green', description: '运行系统健康检查' },
            { name: '性能优化', icon: Zap, color: 'purple', description: '获取性能优化建议' }
          ].map((action, index) => {
            const Icon = action.icon
            return (
              <button
                key={action.name}
                className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group text-center border border-white/50"
              >
                <div className={`w-12 h-12 bg-${action.color}-100 rounded-2xl mx-auto mb-4 flex items-center justify-center group-hover:scale-110 transition-transform`}>
                  <Icon className={`w-6 h-6 text-${action.color}-600`} />
                </div>
                <h4 className="font-semibold text-gray-900 mb-2">{action.name}</h4>
                <p className="text-sm text-gray-600">{action.description}</p>
              </button>
            )
          })}
        </div>
      </section>
    </div>
  )
}

'use client'

import { useState } from 'react'
import Link from 'next/link'
import {
  Globe,
  Share2,
  FileText,
  Users,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  ArrowLeft,
  Plus,
  Search,
  Filter,
  Download,
  RefreshCw,
  Building2,
  Database,
  Eye,
  Edit,
  Trash2,
  Send,
  UserCheck,
  Calendar,
  BarChart3,
  TrendingUp,
  Activity,
  Shield,
  Zap
} from 'lucide-react'

export default function DataSharingPage() {
  const [activeTab, setActiveTab] = useState('overview')
  const [searchTerm, setSearchTerm] = useState('')

  // 共享概览数据
  const sharingOverview = {
    totalRequests: 156,
    approvedRequests: 128,
    pendingRequests: 18,
    rejectedRequests: 10,
    activeDepartments: 24,
    sharedDatasets: 89
  }

  // 共享申请数据
  const sharingRequests = [
    {
      id: 1,
      title: '人口统计数据共享申请',
      applicant: '教育局',
      applicantContact: '张三',
      targetDepartment: '公安局',
      dataType: '人口基础信息',
      purpose: '学区划分和入学资格审核',
      status: 'pending',
      submitTime: '2024-01-15 10:30',
      urgency: 'normal'
    },
    {
      id: 2,
      title: '企业注册信息共享',
      applicant: '税务局',
      applicantContact: '李四',
      targetDepartment: '市场监管局',
      dataType: '企业基础信息',
      purpose: '税务登记和监管执法',
      status: 'approved',
      submitTime: '2024-01-14 14:20',
      urgency: 'high'
    },
    {
      id: 3,
      title: '土地使用权信息申请',
      applicant: '住建局',
      applicantContact: '王五',
      targetDepartment: '自然资源局',
      dataType: '土地权属信息',
      purpose: '建设项目审批',
      status: 'rejected',
      submitTime: '2024-01-13 09:15',
      urgency: 'normal'
    }
  ]

  // 共享协议数据
  const sharingAgreements = [
    {
      id: 1,
      name: '人口信息共享协议',
      parties: ['公安局', '教育局', '民政局'],
      dataTypes: ['人口基础信息', '户籍变更信息'],
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      status: 'active',
      usageCount: 1247
    },
    {
      id: 2,
      name: '企业信息共享协议',
      parties: ['市场监管局', '税务局', '工信局'],
      dataTypes: ['企业注册信息', '经营状态信息'],
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      status: 'active',
      usageCount: 856
    },
    {
      id: 3,
      name: '地理信息共享协议',
      parties: ['自然资源局', '住建局', '交通局'],
      dataTypes: ['地理空间数据', '土地使用信息'],
      startDate: '2023-12-01',
      endDate: '2024-11-30',
      status: 'expiring',
      usageCount: 423
    }
  ]

  // 共享监控数据
  const sharingMonitoring = [
    {
      id: 1,
      department: '教育局',
      dataAccessed: '人口基础信息',
      accessTime: '2024-01-15 14:30',
      recordCount: 1250,
      purpose: '学区划分',
      status: 'success'
    },
    {
      id: 2,
      department: '税务局',
      dataAccessed: '企业注册信息',
      accessTime: '2024-01-15 13:45',
      recordCount: 89,
      purpose: '税务登记',
      status: 'success'
    },
    {
      id: 3,
      department: '住建局',
      dataAccessed: '土地权属信息',
      accessTime: '2024-01-15 11:20',
      recordCount: 0,
      purpose: '项目审批',
      status: 'failed'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': case 'active': case 'success': return 'text-green-600 bg-green-100'
      case 'pending': case 'expiring': return 'text-yellow-600 bg-yellow-100'
      case 'rejected': case 'failed': return 'text-red-600 bg-red-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'high': return 'text-red-600 bg-red-100'
      case 'normal': return 'text-blue-600 bg-blue-100'
      case 'low': return 'text-gray-600 bg-gray-100'
      default: return 'text-gray-600 bg-gray-100'
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 背景装饰 */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-orange-400/20 to-red-400/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-red-400/20 to-orange-400/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>

      <div className="relative z-10">
        {/* 头部导航 */}
        <header className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50">
          <div className="max-w-7xl mx-auto px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Link href="/" className="flex items-center space-x-2 text-gray-600 hover:text-blue-600 transition-colors">
                  <ArrowLeft className="w-5 h-5" />
                  <span>返回首页</span>
                </Link>
                <div className="w-px h-6 bg-gray-300"></div>
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-xl flex items-center justify-center">
                    <Globe className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl font-bold text-gray-900">跨部门数据共享</h1>
                    <p className="text-sm text-gray-600">Cross-Department Data Sharing</p>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                <button className="flex items-center space-x-2 bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors">
                  <Plus className="w-4 h-4" />
                  <span>新建申请</span>
                </button>
                <button className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                  <RefreshCw className="w-4 h-4" />
                  <span>刷新数据</span>
                </button>
                <button className="flex items-center space-x-2 bg-white text-gray-700 px-4 py-2 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                  <Download className="w-4 h-4" />
                  <span>导出报告</span>
                </button>
              </div>
            </div>
          </div>
        </header>

        {/* 主要内容 */}
        <main className="max-w-7xl mx-auto px-6 py-8">
          {/* 标签页导航 */}
          <div className="mb-8">
            <div className="border-b border-gray-200">
              <nav className="-mb-px flex space-x-8">
                {[
                  { id: 'overview', name: '共享概览', icon: BarChart3 },
                  { id: 'requests', name: '申请管理', icon: FileText },
                  { id: 'agreements', name: '共享协议', icon: Users },
                  { id: 'monitoring', name: '使用监控', icon: Activity },
                  { id: 'analytics', name: '数据分析', icon: TrendingUp }
                ].map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                        activeTab === tab.id
                          ? 'border-orange-500 text-orange-600'
                          : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{tab.name}</span>
                    </button>
                  )
                })}
              </nav>
            </div>
          </div>

          {/* 共享概览 */}
          {activeTab === 'overview' && (
            <div className="space-y-8">
              {/* 关键指标卡片 */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">总申请数</p>
                      <p className="text-3xl font-bold text-gray-900">{sharingOverview.totalRequests}</p>
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
                      <FileText className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">已批准</p>
                      <p className="text-3xl font-bold text-green-600">{sharingOverview.approvedRequests}</p>
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                      <CheckCircle className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">待审批</p>
                      <p className="text-3xl font-bold text-yellow-600">{sharingOverview.pendingRequests}</p>
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-amber-500 rounded-xl flex items-center justify-center">
                      <Clock className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">已拒绝</p>
                      <p className="text-3xl font-bold text-red-600">{sharingOverview.rejectedRequests}</p>
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-br from-red-500 to-rose-500 rounded-xl flex items-center justify-center">
                      <XCircle className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">参与部门</p>
                      <p className="text-3xl font-bold text-purple-600">{sharingOverview.activeDepartments}</p>
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl flex items-center justify-center">
                      <Building2 className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">共享数据集</p>
                      <p className="text-3xl font-bold text-indigo-600">{sharingOverview.sharedDatasets}</p>
                    </div>
                    <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-xl flex items-center justify-center">
                      <Database className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>
              </div>

              {/* 共享趋势图表 */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">申请趋势</h3>
                  <div className="h-64 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <TrendingUp className="w-12 h-12 mx-auto mb-2" />
                      <p>申请趋势图表</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">部门参与度</h3>
                  <div className="h-64 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <BarChart3 className="w-12 h-12 mx-auto mb-2" />
                      <p>部门参与度图表</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* 申请管理 */}
          {activeTab === 'requests' && (
            <div className="space-y-6">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-bold text-gray-900">数据共享申请</h3>
                  <button className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors">
                    新建申请
                  </button>
                </div>

                <div className="flex items-center space-x-4 mb-6">
                  <div className="flex-1 relative">
                    <Search className="w-5 h-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="搜索申请标题、申请部门或数据类型..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                    />
                  </div>
                  <button className="flex items-center space-x-2 px-4 py-2 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                    <Filter className="w-4 h-4" />
                    <span>筛选</span>
                  </button>
                </div>

                {/* 申请列表 */}
                <div className="space-y-4">
                  {sharingRequests.map((request) => (
                    <div key={request.id} className="border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h4 className="text-lg font-semibold text-gray-900">{request.title}</h4>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                              {request.status === 'approved' ? '已批准' :
                               request.status === 'pending' ? '待审批' : '已拒绝'}
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getUrgencyColor(request.urgency)}`}>
                              {request.urgency === 'high' ? '紧急' : '普通'}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                            <div>
                              <p className="text-sm text-gray-600">申请部门</p>
                              <p className="font-medium text-gray-900">{request.applicant}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">目标部门</p>
                              <p className="font-medium text-gray-900">{request.targetDepartment}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">数据类型</p>
                              <p className="font-medium text-gray-900">{request.dataType}</p>
                            </div>
                            <div>
                              <p className="text-sm text-gray-600">申请时间</p>
                              <p className="font-medium text-gray-900">{request.submitTime}</p>
                            </div>
                          </div>

                          <div className="mb-4">
                            <p className="text-sm text-gray-600 mb-1">使用目的</p>
                            <p className="text-gray-900">{request.purpose}</p>
                          </div>

                          <div className="flex items-center space-x-2 text-sm text-gray-600">
                            <UserCheck className="w-4 h-4" />
                            <span>联系人: {request.applicantContact}</span>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2 ml-4">
                          <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                            <Eye className="w-4 h-4" />
                          </button>
                          <button className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors">
                            <Edit className="w-4 h-4" />
                          </button>
                          {request.status === 'pending' && (
                            <>
                              <button className="p-2 text-green-600 hover:bg-green-50 rounded-lg transition-colors">
                                <CheckCircle className="w-4 h-4" />
                              </button>
                              <button className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors">
                                <XCircle className="w-4 h-4" />
                              </button>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 共享协议 */}
          {activeTab === 'agreements' && (
            <div className="space-y-6">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                <div className="flex items-center justify-between mb-6">
                  <h3 className="text-lg font-bold text-gray-900">数据共享协议</h3>
                  <button className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors">
                    新建协议
                  </button>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {sharingAgreements.map((agreement) => (
                    <div key={agreement.id} className="border border-gray-200 rounded-xl p-6 hover:shadow-md transition-shadow">
                      <div className="flex items-start justify-between mb-4">
                        <h4 className="text-lg font-semibold text-gray-900">{agreement.name}</h4>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(agreement.status)}`}>
                          {agreement.status === 'active' ? '生效中' : '即将到期'}
                        </span>
                      </div>

                      <div className="space-y-3 mb-4">
                        <div>
                          <p className="text-sm text-gray-600 mb-1">参与部门</p>
                          <div className="flex flex-wrap gap-2">
                            {agreement.parties.map((party, idx) => (
                              <span key={idx} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                                {party}
                              </span>
                            ))}
                          </div>
                        </div>

                        <div>
                          <p className="text-sm text-gray-600 mb-1">数据类型</p>
                          <div className="flex flex-wrap gap-2">
                            {agreement.dataTypes.map((type, idx) => (
                              <span key={idx} className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm">
                                {type}
                              </span>
                            ))}
                          </div>
                        </div>

                        <div className="grid grid-cols-2 gap-4">
                          <div>
                            <p className="text-sm text-gray-600">生效日期</p>
                            <p className="font-medium text-gray-900">{agreement.startDate}</p>
                          </div>
                          <div>
                            <p className="text-sm text-gray-600">到期日期</p>
                            <p className="font-medium text-gray-900">{agreement.endDate}</p>
                          </div>
                        </div>

                        <div>
                          <p className="text-sm text-gray-600">使用次数</p>
                          <p className="text-2xl font-bold text-blue-600">{agreement.usageCount.toLocaleString()}</p>
                        </div>
                      </div>

                      <div className="flex items-center justify-end space-x-2">
                        <button className="text-blue-600 hover:text-blue-800 text-sm">查看详情</button>
                        <button className="text-gray-600 hover:text-gray-800 text-sm">编辑</button>
                        <button className="text-red-600 hover:text-red-800 text-sm">终止</button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 使用监控 */}
          {activeTab === 'monitoring' && (
            <div className="space-y-6">
              <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                <h3 className="text-lg font-bold text-gray-900 mb-6">数据访问监控</h3>

                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 font-medium text-gray-600">访问部门</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">访问数据</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">访问时间</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">记录数量</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">使用目的</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">状态</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-600">操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      {sharingMonitoring.map((record) => (
                        <tr key={record.id} className="border-b border-gray-100 hover:bg-gray-50/50">
                          <td className="py-4 px-4">
                            <div className="flex items-center space-x-3">
                              <div className="w-8 h-8 bg-gradient-to-br from-orange-500 to-red-500 rounded-full flex items-center justify-center">
                                <Building2 className="w-4 h-4 text-white" />
                              </div>
                              <span className="font-medium text-gray-900">{record.department}</span>
                            </div>
                          </td>
                          <td className="py-4 px-4 text-gray-600">{record.dataAccessed}</td>
                          <td className="py-4 px-4 text-gray-600">{record.accessTime}</td>
                          <td className="py-4 px-4">
                            <span className="font-medium text-gray-900">{record.recordCount.toLocaleString()}</span>
                          </td>
                          <td className="py-4 px-4 text-gray-600">{record.purpose}</td>
                          <td className="py-4 px-4">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(record.status)}`}>
                              {record.status === 'success' ? '成功' : '失败'}
                            </span>
                          </td>
                          <td className="py-4 px-4">
                            <div className="flex items-center space-x-2">
                              <button className="text-blue-600 hover:text-blue-800 text-sm">详情</button>
                              <button className="text-gray-600 hover:text-gray-800 text-sm">日志</button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          )}

          {/* 数据分析 */}
          {activeTab === 'analytics' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">热门数据类型</h3>
                  <div className="h-64 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <BarChart3 className="w-12 h-12 mx-auto mb-2" />
                      <p>热门数据类型统计图</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">部门协作网络</h3>
                  <div className="h-64 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <Share2 className="w-12 h-12 mx-auto mb-2" />
                      <p>部门协作关系图</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">使用频率分析</h3>
                  <div className="h-64 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <Activity className="w-12 h-12 mx-auto mb-2" />
                      <p>数据使用频率图</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
                  <h3 className="text-lg font-bold text-gray-900 mb-4">效率提升分析</h3>
                  <div className="h-64 flex items-center justify-center text-gray-500">
                    <div className="text-center">
                      <TrendingUp className="w-12 h-12 mx-auto mb-2" />
                      <p>效率提升趋势图</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  )
}

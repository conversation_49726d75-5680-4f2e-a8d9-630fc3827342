'use client'

import { Database, Table, BarChart3, Users, Calendar, Download, Eye, Lock } from 'lucide-react'

export default function DataDirectoryPage() {
  const databases = [
    {
      id: 1,
      name: '人口基础数据库',
      description: '全市人口基础信息数据库，包含户籍、身份等信息',
      tables: 12,
      records: '2,400,000',
      size: '2.4TB',
      department: '公安局',
      access: 'restricted',
      lastUpdated: '2024-01-15',
      schema: [
        { name: 'person_info', records: '2,400,000', description: '人员基础信息表' },
        { name: 'household', records: '800,000', description: '户籍信息表' },
        { name: 'identity_card', records: '2,400,000', description: '身份证信息表' }
      ]
    },
    {
      id: 2,
      name: '企业注册信息库',
      description: '企业工商注册、变更、注销等全生命周期数据',
      tables: 8,
      records: '156,000',
      size: '156GB',
      department: '市场监管局',
      access: 'public',
      lastUpdated: '2024-01-14',
      schema: [
        { name: 'enterprise_basic', records: '156,000', description: '企业基本信息表' },
        { name: 'registration_change', records: '89,000', description: '变更记录表' },
        { name: 'business_scope', records: '156,000', description: '经营范围表' }
      ]
    },
    {
      id: 3,
      name: '地理信息数据库',
      description: '全市地理空间数据，包含地形、建筑、道路等信息',
      tables: 15,
      records: '89,000',
      size: '890GB',
      department: '自然资源局',
      access: 'internal',
      lastUpdated: '2024-01-13',
      schema: [
        { name: 'buildings', records: '45,000', description: '建筑物信息表' },
        { name: 'roads', records: '12,000', description: '道路网络表' },
        { name: 'land_use', records: '32,000', description: '土地利用表' }
      ]
    }
  ]

  const getAccessIcon = (access: string) => {
    switch (access) {
      case 'public': return <Eye className="w-4 h-4 text-green-500" />
      case 'internal': return <Users className="w-4 h-4 text-yellow-500" />
      case 'restricted': return <Lock className="w-4 h-4 text-red-500" />
      default: return <Lock className="w-4 h-4 text-gray-500" />
    }
  }

  const getAccessText = (access: string) => {
    switch (access) {
      case 'public': return '公开'
      case 'internal': return '内部'
      case 'restricted': return '受限'
      default: return '未知'
    }
  }

  const getAccessColor = (access: string) => {
    switch (access) {
      case 'public': return 'bg-green-100 text-green-700'
      case 'internal': return 'bg-yellow-100 text-yellow-700'
      case 'restricted': return 'bg-red-100 text-red-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">数据目录</h1>
        <p className="text-xl text-gray-600">浏览和管理结构化数据资源</p>
      </div>

      {/* 统计概览 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">数据库总数</p>
              <p className="text-3xl font-bold text-gray-900">{databases.length}</p>
            </div>
            <Database className="w-8 h-8 text-indigo-500" />
          </div>
        </div>
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">数据表总数</p>
              <p className="text-3xl font-bold text-gray-900">{databases.reduce((sum, db) => sum + db.tables, 0)}</p>
            </div>
            <Table className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">总记录数</p>
              <p className="text-3xl font-bold text-gray-900">5M+</p>
            </div>
            <BarChart3 className="w-8 h-8 text-green-500" />
          </div>
        </div>
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">存储容量</p>
              <p className="text-3xl font-bold text-gray-900">3.4TB</p>
            </div>
            <Database className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* 数据库列表 */}
      <div className="space-y-6">
        {databases.map((database) => (
          <div
            key={database.id}
            className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden"
          >
            {/* 数据库头部信息 */}
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-xl font-bold text-gray-900">{database.name}</h3>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getAccessColor(database.access)}`}>
                      {getAccessText(database.access)}
                    </span>
                  </div>
                  <p className="text-gray-600 mb-4">{database.description}</p>
                  <div className="flex items-center space-x-6 text-sm text-gray-600">
                    <span className="flex items-center space-x-1">
                      <Table className="w-4 h-4" />
                      <span>{database.tables} 张表</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <BarChart3 className="w-4 h-4" />
                      <span>{database.records} 条记录</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <Database className="w-4 h-4" />
                      <span>{database.size}</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <Users className="w-4 h-4" />
                      <span>{database.department}</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <Calendar className="w-4 h-4" />
                      <span>更新: {database.lastUpdated}</span>
                    </span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getAccessIcon(database.access)}
                  <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-xl flex items-center justify-center">
                    <Database className="w-8 h-8 text-white" />
                  </div>
                </div>
              </div>
            </div>

            {/* 数据表结构 */}
            <div className="p-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">数据表结构</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {database.schema.map((table, index) => (
                  <div
                    key={index}
                    className="bg-gray-50/50 rounded-xl p-4 hover:bg-gray-100/50 transition-colors"
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      <Table className="w-5 h-5 text-indigo-500" />
                      <h5 className="font-medium text-gray-900">{table.name}</h5>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{table.description}</p>
                    <p className="text-xs text-gray-500">{table.records} 条记录</p>
                  </div>
                ))}
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="px-6 py-4 bg-gray-50/30 border-t border-gray-100">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <button className="text-indigo-600 hover:text-indigo-700 font-medium text-sm transition-colors">
                    查看详情
                  </button>
                  <button className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors flex items-center space-x-1">
                    <Eye className="w-4 h-4" />
                    <span>预览数据</span>
                  </button>
                  <button className="text-green-600 hover:text-green-700 font-medium text-sm transition-colors flex items-center space-x-1">
                    <Download className="w-4 h-4" />
                    <span>导出</span>
                  </button>
                </div>
                <button className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors text-sm font-medium">
                  申请访问
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default function SettingsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">系统设置</h1>
        <p className="mt-1 text-gray-600">系统配置与参数管理</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* 设置菜单 */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-semibold text-gray-900">设置分类</h3>
          </div>
          <div className="card-content">
            <nav className="space-y-2">
              {[
                { name: '基础设置', active: true },
                { name: '用户管理', active: false },
                { name: '权限配置', active: false },
                { name: '数据源配置', active: false },
                { name: '监控告警', active: false },
                { name: '系统日志', active: false },
                { name: '备份恢复', active: false },
              ].map((item) => (
                <button
                  key={item.name}
                  className={`w-full text-left px-3 py-2 rounded-lg transition-colors ${
                    item.active 
                      ? 'bg-primary-100 text-primary-700 font-medium' 
                      : 'text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  {item.name}
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* 设置内容 */}
        <div className="lg:col-span-2 space-y-6">
          {/* 基础设置 */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900">基础设置</h3>
            </div>
            <div className="card-content space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  系统名称
                </label>
                <input
                  type="text"
                  defaultValue="云宇政数平台"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  系统描述
                </label>
                <textarea
                  rows={3}
                  defaultValue="统一的政数局数据管理平台"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  时区设置
                </label>
                <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent">
                  <option>Asia/Shanghai (UTC+8)</option>
                  <option>Asia/Beijing (UTC+8)</option>
                </select>
              </div>
            </div>
          </div>

          {/* 性能设置 */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900">性能设置</h3>
            </div>
            <div className="card-content space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-gray-900">启用缓存</div>
                  <div className="text-sm text-gray-500">提高系统响应速度</div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" defaultChecked className="sr-only peer" />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>
              
              <div className="flex items-center justify-between">
                <div>
                  <div className="font-medium text-gray-900">自动备份</div>
                  <div className="text-sm text-gray-500">定期自动备份重要数据</div>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input type="checkbox" defaultChecked className="sr-only peer" />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                </label>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  数据刷新间隔 (秒)
                </label>
                <input
                  type="number"
                  defaultValue="30"
                  min="5"
                  max="300"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* 保存按钮 */}
          <div className="flex justify-end space-x-3">
            <button className="btn-secondary">
              重置
            </button>
            <button className="btn-primary">
              保存设置
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ HomePage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/api/link.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/monitor.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/database.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/git-merge.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/network.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/bar-chart-3.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/building-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/cpu.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Award,BarChart3,Building2,CheckCircle,Cpu,Database,Eye,FileText,GitMerge,Globe,HardDrive,Monitor,Network,Server,Shield,Sparkles,Target,TrendingUp,Users,Zap!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/target.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction HomePage() {\n    _s();\n    const [showModuleOverlay, setShowModuleOverlay] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showLifecycleModules, setShowLifecycleModules] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    // 业务模块数据\n    const businessModules = [\n        {\n            name: \"数据大屏\",\n            description: \"实时数据可视化展示，支持多种图表和指标监控\",\n            href: \"/screen\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            gradient: \"from-blue-500 to-cyan-500\",\n            logo: \"\\uD83D\\uDCCA\",\n            features: [\n                \"实时监控\",\n                \"可视化图表\",\n                \"大屏展示\"\n            ]\n        },\n        {\n            name: \"报表分析\",\n            description: \"多维度数据分析报表，支持自定义报表生成\",\n            href: \"/reports\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            gradient: \"from-green-500 to-emerald-500\",\n            logo: \"\\uD83D\\uDCC8\",\n            features: [\n                \"多维分析\",\n                \"自定义报表\",\n                \"数据导出\"\n            ]\n        },\n        {\n            name: \"数据采集\",\n            description: \"多源数据采集接入，支持实时和批量数据导入\",\n            href: \"/collection\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            gradient: \"from-purple-500 to-violet-500\",\n            logo: \"\\uD83D\\uDD04\",\n            features: [\n                \"多源接入\",\n                \"实时采集\",\n                \"数据清洗\"\n            ]\n        },\n        {\n            name: \"数据汇聚\",\n            description: \"跨系统数据整合汇聚，统一数据标准和格式\",\n            href: \"/aggregation\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            gradient: \"from-orange-500 to-amber-500\",\n            logo: \"\\uD83D\\uDD17\",\n            features: [\n                \"数据整合\",\n                \"标准化\",\n                \"质量控制\"\n            ]\n        },\n        {\n            name: \"数据治理\",\n            description: \"数据质量管控，数据清洗、去重、标准化处理\",\n            href: \"/governance\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            gradient: \"from-red-500 to-rose-500\",\n            logo: \"\\uD83D\\uDEE1️\",\n            features: [\n                \"质量管控\",\n                \"数据清洗\",\n                \"合规管理\"\n            ]\n        },\n        {\n            name: \"资源池管理\",\n            description: \"数据资源统一管理，资源目录和权限控制\",\n            href: \"/resources\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n            gradient: \"from-indigo-500 to-blue-500\",\n            logo: \"\\uD83D\\uDCBE\",\n            features: [\n                \"资源目录\",\n                \"权限管理\",\n                \"存储优化\"\n            ]\n        },\n        {\n            name: \"设备监控\",\n            description: \"服务器设备实时监控，性能指标和告警管理\",\n            href: \"/monitoring\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n            gradient: \"from-teal-500 to-cyan-500\",\n            logo: \"\\uD83D\\uDDA5️\",\n            features: [\n                \"实时监控\",\n                \"性能分析\",\n                \"告警管理\"\n            ]\n        },\n        {\n            name: \"网络管理\",\n            description: \"多物理网络管理，网络拓扑和流量监控\",\n            href: \"/network\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n            gradient: \"from-pink-500 to-rose-500\",\n            logo: \"\\uD83C\\uDF10\",\n            features: [\n                \"网络拓扑\",\n                \"流量监控\",\n                \"安全管理\"\n            ]\n        }\n    ];\n    // 核心能力\n    const capabilities = [\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"数据全生命周期管理\",\n            description: \"从数据采集、清洗、存储到应用的全流程管理，确保数据质量和安全性\",\n            gradient: \"from-blue-500 to-cyan-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n            title: \"智能数据分析\",\n            description: \"多维度数据分析和可视化展示，支持实时监控和预测分析\",\n            gradient: \"from-green-500 to-emerald-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"数据安全治理\",\n            description: \"完善的数据安全体系和质量管控机制，保障数据合规使用\",\n            gradient: \"from-purple-500 to-violet-500\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n            title: \"跨部门数据共享\",\n            description: \"打破数据孤岛，实现政务数据统一共享和协同应用\",\n            gradient: \"from-orange-500 to-red-500\"\n        }\n    ];\n    // 解决的场景\n    const scenarios = [\n        {\n            title: \"政府决策支撑\",\n            description: \"为领导层提供实时、准确的数据分析，支撑科学决策\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"]\n        },\n        {\n            title: \"跨部门协同\",\n            description: \"消除信息孤岛，实现部门间数据无缝对接和业务协同\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        },\n        {\n            title: \"公共服务优化\",\n            description: \"通过数据分析优化公共服务流程，提升市民满意度\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"]\n        },\n        {\n            title: \"监管效能提升\",\n            description: \"利用大数据技术提升监管效率和精准度\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"]\n        }\n    ];\n    // 数据来源单位\n    const dataSourceUnits = [\n        \"市政府办公厅\",\n        \"发展改革委\",\n        \"教育局\",\n        \"科技局\",\n        \"工信局\",\n        \"公安局\",\n        \"民政局\",\n        \"司法局\",\n        \"财政局\",\n        \"人社局\",\n        \"自然资源局\",\n        \"生态环境局\",\n        \"住建局\",\n        \"交通运输局\",\n        \"水务局\",\n        \"农业农村局\",\n        \"商务局\",\n        \"文旅局\",\n        \"卫健委\",\n        \"应急管理局\",\n        \"审计局\",\n        \"市场监管局\",\n        \"统计局\",\n        \"医保局\"\n    ];\n    // 权威数据领域\n    const authorityData = [\n        {\n            area: \"人口信息\",\n            coverage: \"100%\",\n            source: \"公安、民政、人社等部门\",\n            color: \"blue\"\n        },\n        {\n            area: \"企业信息\",\n            coverage: \"100%\",\n            source: \"市场监管、税务、工信等部门\",\n            color: \"green\"\n        },\n        {\n            area: \"地理信息\",\n            coverage: \"100%\",\n            source: \"自然资源、住建、交通等部门\",\n            color: \"purple\"\n        },\n        {\n            area: \"经济数据\",\n            coverage: \"95%\",\n            source: \"统计、财政、发改等部门\",\n            color: \"orange\"\n        },\n        {\n            area: \"社会事业\",\n            coverage: \"90%\",\n            source: \"教育、卫健、文旅等部门\",\n            color: \"pink\"\n        },\n        {\n            area: \"环境数据\",\n            coverage: \"100%\",\n            source: \"生态环境、水务、应急等部门\",\n            color: \"teal\"\n        }\n    ];\n    // 目标客户\n    const targetUsers = [\n        {\n            type: \"政府决策层\",\n            desc: \"为各级领导提供数据支撑和分析报告，辅助科学决策\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n            features: [\n                \"实时数据大屏\",\n                \"决策分析报告\",\n                \"趋势预测分析\"\n            ]\n        },\n        {\n            type: \"业务部门\",\n            desc: \"各委办局日常业务数据管理和跨部门协同应用\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n            features: [\n                \"业务数据管理\",\n                \"跨部门协同\",\n                \"流程优化\"\n            ]\n        },\n        {\n            type: \"技术人员\",\n            desc: \"数据开发、运维和技术支持人员的专业工具\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n            features: [\n                \"数据开发工具\",\n                \"系统监控\",\n                \"技术支持\"\n            ]\n        },\n        {\n            type: \"公众服务\",\n            desc: \"为市民和企业提供便民服务和信息查询\",\n            icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n            features: [\n                \"信息查询\",\n                \"在线服务\",\n                \"便民应用\"\n            ]\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 pointer-events-none overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-float\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 212,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-indigo-400/20 to-purple-400/20 rounded-full blur-3xl animate-float\",\n                        style: {\n                            animationDelay: \"2s\"\n                        }\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 213,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 211,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"max-w-7xl mx-auto px-6 py-20\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"text-center mb-20 animate-fade-in\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"inline-flex items-center space-x-2 bg-blue-50/50 backdrop-blur-sm px-6 py-3 rounded-full mb-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"w-5 h-5 text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg font-medium text-blue-700\",\n                                                children: \"智慧政务 \\xb7 数据驱动 \\xb7 创新未来\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 223,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-6xl md:text-7xl font-bold mb-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                            children: \"云宇政数平台\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-2xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed\",\n                                        children: \"统一的政务数据管理与服务平台，为政府数字化转型提供全方位的数据支撑， 实现跨部门数据共享、智能分析决策和高效政务服务\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-center space-x-8 mb-12 text-lg text-gray-500\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 237,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"24个委办局接入\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 238,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"2.4TB数据存储\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 242,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"w-6 h-6 text-green-500\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"99.9%系统可用性\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 246,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 244,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: showModuleOverlay ? \"业务模块快速入口\" : \"平台核心能力\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: showModuleOverlay ? \"选择您需要的功能模块，直接进入对应的业务操作界面\" : \"为政务数字化转型提供全方位的技术支撑\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this),\n                                     true ? // 原核心能力内容\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8\",\n                                        children: [\n                                            capabilities.map((capability, index)=>{\n                                                const Icon = capability.icon;\n                                                const isLifecycleCard = capability.title === \"数据全生命周期管理\";\n                                                const isSecurityCard = capability.title === \"数据安全治理\";\n                                                const isSharingCard = capability.title === \"跨部门数据共享\";\n                                                const handleCardClick = ()=>{\n                                                    if (isSecurityCard) {\n                                                        window.location.href = \"/security-governance\";\n                                                    } else if (isSharingCard) {\n                                                        window.location.href = \"/data-sharing\";\n                                                    }\n                                                };\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20 \".concat(isSecurityCard || isSharingCard ? \"cursor-pointer\" : \"\"),\n                                                            style: {\n                                                                animationDelay: \"\".concat(index * 100, \"ms\")\n                                                            },\n                                                            onMouseEnter: ()=>{\n                                                                if (isLifecycleCard) {\n                                                                    setShowLifecycleModules(true);\n                                                                }\n                                                            },\n                                                            onMouseLeave: ()=>{\n                                                                if (isLifecycleCard) {\n                                                                    setShowLifecycleModules(false);\n                                                                }\n                                                            },\n                                                            onClick: handleCardClick,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"w-16 h-16 bg-gradient-to-br \".concat(capability.gradient, \" rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110\"),\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                            className: \"w-8 h-8 text-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 298,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 297,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                                                                        children: capability.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 300,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-gray-600 leading-relaxed\",\n                                                                        children: capability.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 301,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 296,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 281,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        isLifecycleCard && showLifecycleModules && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute left-full top-0 ml-4 z-50 animate-fade-in\",\n                                                            onMouseEnter: ()=>setShowLifecycleModules(true),\n                                                            onMouseLeave: ()=>setShowLifecycleModules(false),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 gap-4 w-80\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                        href: \"/collection\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-white/95 backdrop-blur-sm rounded-xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 group cursor-pointer border border-white/50\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-12 h-12 bg-gradient-to-br from-purple-500 to-violet-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                                                            className: \"w-6 h-6 text-white\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 318,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 317,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                className: \"text-lg font-bold text-gray-900 group-hover:text-purple-700 transition-colors\",\n                                                                                                children: \"数据采集\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 321,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm text-gray-600\",\n                                                                                                children: \"多源数据采集接入\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 322,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 320,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"w-5 h-5 text-gray-400 group-hover:text-purple-600 transform group-hover:translate-x-1 transition-all\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 324,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 316,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 315,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                        href: \"/aggregation\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-white/95 backdrop-blur-sm rounded-xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 group cursor-pointer border border-white/50\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-12 h-12 bg-gradient-to-br from-orange-500 to-amber-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                                            className: \"w-6 h-6 text-white\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 334,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 333,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                className: \"text-lg font-bold text-gray-900 group-hover:text-orange-700 transition-colors\",\n                                                                                                children: \"数据汇聚\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 337,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm text-gray-600\",\n                                                                                                children: \"跨系统数据整合\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 338,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 336,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"w-5 h-5 text-gray-400 group-hover:text-orange-600 transform group-hover:translate-x-1 transition-all\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 340,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 332,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 331,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 330,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                                        href: \"/governance\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"bg-white/95 backdrop-blur-sm rounded-xl p-6 shadow-xl hover:shadow-2xl transition-all duration-300 group cursor-pointer border border-white/50\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center space-x-4\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"w-12 h-12 bg-gradient-to-br from-red-500 to-rose-500 rounded-xl flex items-center justify-center shadow-lg\",\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                                            className: \"w-6 h-6 text-white\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 350,\n                                                                                            columnNumber: 37\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 349,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex-1\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                                                className: \"text-lg font-bold text-gray-900 group-hover:text-red-700 transition-colors\",\n                                                                                                children: \"数据治理\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 353,\n                                                                                                columnNumber: 37\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                className: \"text-sm text-gray-600\",\n                                                                                                children: \"数据质量管控\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 354,\n                                                                                                columnNumber: 37\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 352,\n                                                                                        columnNumber: 35\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                        className: \"w-5 h-5 text-gray-400 group-hover:text-red-600 transform group-hover:translate-x-1 transition-all\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 356,\n                                                                                        columnNumber: 35\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 348,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 347,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 346,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 312,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 307,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, capability.title, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 280,\n                                                    columnNumber: 21\n                                                }, this);\n                                            }),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"relative\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    href: \"/dashboard\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gradient-to-br from-blue-600 to-indigo-600 rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 group border border-blue-300/50 cursor-pointer transform hover:scale-105\",\n                                                        style: {\n                                                            animationDelay: \"400ms\"\n                                                        },\n                                                        onMouseEnter: ()=>setShowModuleOverlay(true),\n                                                        onMouseLeave: ()=>setShowModuleOverlay(false),\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"w-16 h-16 bg-white/20 backdrop-blur-sm rounded-2xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                        className: \"w-8 h-8 text-white\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 378,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 377,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-white mb-4\",\n                                                                    children: \"立即体验\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-100 leading-relaxed mb-4\",\n                                                                    children: \"悬停查看业务模块，点击进入云宇政数平台\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 381,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-center\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white font-bold flex items-center group-hover:translate-x-1 transition-transform\",\n                                                                        children: [\n                                                                            \"进入平台\",\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                className: \"w-5 h-5 ml-2\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 385,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 383,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 382,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 264,\n                                        columnNumber: 15\n                                    }, this) : // 业务模块内容\n                                    /*#__PURE__*/ 0\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"200ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"解决的应用场景\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"覆盖政务管理的各个关键环节，提升政府治理效能\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 455,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                                        children: scenarios.map((scenario, index)=>{\n                                            const Icon = scenario.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20\",\n                                                style: {\n                                                    animationDelay: \"\".concat(index * 100, \"ms\")\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-start space-x-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center flex-shrink-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                className: \"w-6 h-6 text-white\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 469,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 468,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                    className: \"text-xl font-bold text-gray-900 mb-3\",\n                                                                    children: scenario.title\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 472,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-600 leading-relaxed\",\n                                                                    children: scenario.description\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                    lineNumber: 473,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 471,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 467,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, scenario.title, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"400ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"数据来源单位\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"覆盖全市24个主要委办局，构建统一的数据生态体系\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 484,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-6\",\n                                            children: dataSourceUnits.map((unit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4 rounded-xl bg-gray-50/50 hover:bg-blue-50/50 transition-all duration-300 group\",\n                                                    style: {\n                                                        animationDelay: \"\".concat(index * 50, \"ms\")\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                            className: \"w-8 h-8 text-gray-400 group-hover:text-blue-500 mx-auto mb-3 transition-colors\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 497,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium text-gray-700 group-hover:text-blue-700 transition-colors\",\n                                                            children: unit\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 498,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, unit, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 492,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 490,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 489,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 483,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"600ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"权威数据覆盖\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 508,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"全面覆盖政务核心数据领域，确保数据权威性和完整性\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 509,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                                        children: authorityData.map((data, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group border border-white/20\",\n                                                style: {\n                                                    animationDelay: \"\".concat(index * 100, \"ms\")\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center justify-between mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                className: \"text-xl font-bold text-gray-900\",\n                                                                children: data.area\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 520,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-3xl font-bold text-blue-600\",\n                                                                children: data.coverage\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 521,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 519,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-6\",\n                                                        children: data.source\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 523,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-full bg-gray-200 rounded-full h-3\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-gradient-to-r from-blue-500 to-indigo-500 h-3 rounded-full transition-all duration-1000\",\n                                                            style: {\n                                                                width: data.coverage\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 525,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 524,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, data.area, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 514,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 512,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"800ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                                children: \"服务对象\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 538,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xl text-gray-600\",\n                                                children: \"为不同类型用户提供专业化的数据服务和解决方案\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 539,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 537,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                        children: targetUsers.map((user, index)=>{\n                                            const Icon = user.icon;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-500 group text-center border border-white/20\",\n                                                style: {\n                                                    animationDelay: \"\".concat(index * 100, \"ms\")\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-2xl mx-auto mb-6 flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                            className: \"w-8 h-8 text-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 552,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-bold text-gray-900 mb-4\",\n                                                        children: user.type\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 554,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600 mb-6 leading-relaxed\",\n                                                        children: user.desc\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: user.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-center text-sm text-gray-500\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                        className: \"w-4 h-4 text-green-500 mr-2\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 559,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    feature\n                                                                ]\n                                                            }, idx, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 558,\n                                                                columnNumber: 25\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 556,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, user.type, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 546,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 542,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 536,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"mb-20 animate-slide-up\",\n                                style: {\n                                    animationDelay: \"1000ms\"\n                                },\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center mb-12\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"inline-flex items-center space-x-3 bg-gradient-to-r from-blue-50 to-indigo-50 px-8 py-4 rounded-2xl mb-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"w-8 h-8 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 574,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent\",\n                                                        children: \"业务模块入口\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 575,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 573,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-5xl font-bold text-gray-900 mb-6\",\n                                                children: \"选择您需要的功能模块\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 577,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-2xl text-gray-600\",\n                                                children: \"点击下方模块卡片，直接进入对应的业务操作界面\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 578,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 572,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gradient-to-r from-blue-500/10 to-indigo-500/10 rounded-3xl p-12 border-2 border-blue-200/50 shadow-2xl\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                                                children: [\n                                                    {\n                                                        name: \"数据大屏\",\n                                                        description: \"实时数据可视化展示，支持多种图表和指标监控\",\n                                                        href: \"/dashboard/screen\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                                                        gradient: \"from-blue-500 to-cyan-500\",\n                                                        features: [\n                                                            \"实时监控\",\n                                                            \"可视化图表\",\n                                                            \"大屏展示\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"报表分析\",\n                                                        description: \"多维度数据分析报表，支持自定义报表生成\",\n                                                        href: \"/dashboard/reports\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                                                        gradient: \"from-green-500 to-emerald-500\",\n                                                        features: [\n                                                            \"多维分析\",\n                                                            \"自定义报表\",\n                                                            \"数据导出\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"数据采集\",\n                                                        description: \"多源数据采集接入，支持实时和批量数据导入\",\n                                                        href: \"/dashboard/collection\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                                                        gradient: \"from-purple-500 to-violet-500\",\n                                                        features: [\n                                                            \"多源接入\",\n                                                            \"实时采集\",\n                                                            \"数据清洗\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"数据汇聚\",\n                                                        description: \"跨系统数据整合汇聚，统一数据标准和格式\",\n                                                        href: \"/dashboard/aggregation\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                                        gradient: \"from-orange-500 to-amber-500\",\n                                                        features: [\n                                                            \"数据整合\",\n                                                            \"标准化\",\n                                                            \"质量控制\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"数据治理\",\n                                                        description: \"数据质量管控，数据清洗、去重、标准化处理\",\n                                                        href: \"/dashboard/governance\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                                        gradient: \"from-red-500 to-rose-500\",\n                                                        features: [\n                                                            \"质量管控\",\n                                                            \"数据清洗\",\n                                                            \"合规管理\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"资源池管理\",\n                                                        description: \"数据资源统一管理，资源目录和权限控制\",\n                                                        href: \"/dashboard/resources\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                                        gradient: \"from-indigo-500 to-blue-500\",\n                                                        features: [\n                                                            \"资源目录\",\n                                                            \"权限管理\",\n                                                            \"存储优化\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"设备监控\",\n                                                        description: \"服务器设备实时监控，性能指标和告警管理\",\n                                                        href: \"/dashboard/monitoring\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                                        gradient: \"from-teal-500 to-cyan-500\",\n                                                        features: [\n                                                            \"实时监控\",\n                                                            \"性能分析\",\n                                                            \"告警管理\"\n                                                        ]\n                                                    },\n                                                    {\n                                                        name: \"网络管理\",\n                                                        description: \"多物理网络管理，网络拓扑和流量监控\",\n                                                        href: \"/dashboard/network\",\n                                                        icon: _barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                                        gradient: \"from-pink-500 to-rose-500\",\n                                                        features: [\n                                                            \"网络拓扑\",\n                                                            \"流量监控\",\n                                                            \"安全管理\"\n                                                        ]\n                                                    }\n                                                ].map((module, index)=>{\n                                                    const Icon = module.icon;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                        href: module.href,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"bg-white/90 backdrop-blur-sm rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 cursor-pointer group transform hover:scale-105 border border-white/50\",\n                                                            style: {\n                                                                animationDelay: \"\".concat(index * 100, \"ms\")\n                                                            },\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"relative overflow-hidden text-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 bg-gradient-to-br \".concat(module.gradient, \" opacity-0 group-hover:opacity-10 transition-opacity duration-500 rounded-3xl\")\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 659,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"relative z-10\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"w-20 h-20 bg-gradient-to-br \".concat(module.gradient, \" rounded-3xl mx-auto mb-6 flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110\"),\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                                                    className: \"w-10 h-10 text-white\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 663,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 662,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"text-xl font-bold text-gray-900 mb-4 group-hover:text-blue-700 transition-colors\",\n                                                                                children: module.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 666,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                className: \"text-gray-600 text-sm mb-6 leading-relaxed\",\n                                                                                children: module.description\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 670,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"space-y-2 mb-6\",\n                                                                                children: module.features.map((feature, idx)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"flex items-center justify-center text-xs text-gray-500\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                                                className: \"w-3 h-3 text-green-500 mr-2\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                                lineNumber: 677,\n                                                                                                columnNumber: 35\n                                                                                            }, this),\n                                                                                            feature\n                                                                                        ]\n                                                                                    }, idx, true, {\n                                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                        lineNumber: 676,\n                                                                                        columnNumber: 33\n                                                                                    }, this))\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 674,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center justify-center\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-lg font-bold text-blue-600 group-hover:text-blue-700 transition-colors flex items-center\",\n                                                                                    children: [\n                                                                                        \"立即使用\",\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                                            className: \"w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                            lineNumber: 686,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                    lineNumber: 684,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                                lineNumber: 683,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 661,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white/50 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-1000\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                            lineNumber: 693,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                                lineNumber: 657,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 653,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, module.name, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 652,\n                                                        columnNumber: 21\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 583,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center mt-12\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-flex items-center space-x-3 text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-6 h-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 705,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg\",\n                                                            children: \"选择任意模块开始您的数据管理之旅\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 706,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 704,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 703,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 582,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 571,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                                className: \"text-center animate-slide-up\",\n                                style: {\n                                    animationDelay: \"1200ms\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-gradient-to-r from-blue-600 to-indigo-600 rounded-3xl p-12 text-white shadow-2xl\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-4xl font-bold mb-6\",\n                                            children: \"准备好开始了吗？\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 715,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-2xl mb-12 text-blue-100\",\n                                            children: \"立即体验云宇政数平台，开启您的智慧政务数据管理之旅\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 716,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row items-center justify-center space-y-6 sm:space-y-0 sm:space-x-8\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                                    href: \"/dashboard\",\n                                                    className: \"bg-white text-blue-600 hover:bg-blue-50 font-bold py-5 px-12 rounded-2xl transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl flex items-center text-xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"w-6 h-6 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 724,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"进入平台\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 720,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    className: \"bg-blue-500/20 backdrop-blur-sm text-white hover:bg-blue-500/30 font-medium py-5 px-12 rounded-2xl transition-all duration-300 border border-white/20 flex items-center text-xl\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Award_BarChart3_Building2_CheckCircle_Cpu_Database_Eye_FileText_GitMerge_Globe_HardDrive_Monitor_Network_Server_Shield_Sparkles_Target_TrendingUp_Users_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-6 h-6 mr-3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 728,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"了解更多\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 727,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 719,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 714,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 713,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 218,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                        className: \"bg-white/80 backdrop-blur-xl border-t border-white/20 mt-20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-7xl mx-auto px-6 py-12\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center text-gray-600\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg mb-3\",\n                                        children: \"\\xa9 2024 云宇政数平台. 保留所有权利.\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 740,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-base\",\n                                        children: \"为政府数字化转型提供专业的数据管理服务\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 741,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 739,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 738,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 737,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 216,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 209,\n        columnNumber: 5\n    }, this);\n}\n_s(HomePage, \"CxmuX3o+PN3XC6UILmiLyOd/Dl4=\");\n_c = HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ })

});
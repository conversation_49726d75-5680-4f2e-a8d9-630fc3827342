'use client'

import { useState } from 'react'
import { Git<PERSON><PERSON>ge, Setting<PERSON>, BarChart3 } from 'lucide-react'
import TaskManagement from './components/TaskManagement'
import ChannelManagement from './components/ChannelManagement'

export default function AggregationSystemLayout() {
  const [activeTab, setActiveTab] = useState<'tasks' | 'channels'>('tasks')

  const renderContent = () => {
    switch (activeTab) {
      case 'tasks':
        return <TaskManagement />
      case 'channels':
        return <ChannelManagement />
      default:
        return <TaskManagement />
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* 系统头部 */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-white/20 sticky top-0 z-50">
        <div className="mx-auto px-8 py-4" style={{ maxWidth: 'calc(100vw - 4rem)' }}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              {/* 系统Logo */}
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center shadow-lg">
                  <GitMerge className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                    🔗 数据汇聚系统
                  </h1>
                  <p className="text-sm text-gray-600">跨系统数据整合汇聚平台</p>
                </div>
              </div>

              {/* 导航菜单 */}
              <div className="flex space-x-6 ml-8">
                <button
                  onClick={() => setActiveTab('tasks')}
                  className={`py-2 px-4 rounded-lg font-medium text-sm transition-colors flex items-center space-x-2 ${
                    activeTab === 'tasks'
                      ? 'bg-blue-100 text-blue-600 border border-blue-200'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <Settings className="w-4 h-4" />
                  <span>任务管理</span>
                </button>
                <button
                  onClick={() => setActiveTab('channels')}
                  className={`py-2 px-4 rounded-lg font-medium text-sm transition-colors flex items-center space-x-2 ${
                    activeTab === 'channels'
                      ? 'bg-blue-100 text-blue-600 border border-blue-200'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                  }`}
                >
                  <BarChart3 className="w-4 h-4" />
                  <span>通道管理</span>
                </button>
              </div>
            </div>

            {/* 返回主平台 */}
            <div className="flex items-center space-x-4">
              <a
                href="/"
                className="text-gray-600 hover:text-blue-600 transition-colors text-sm font-medium"
              >
                返回首页
              </a>
              <a
                href="/dashboard"
                className="bg-blue-600 text-white px-4 py-2 rounded-xl hover:bg-blue-700 transition-colors text-sm font-medium"
              >
                主控台
              </a>
            </div>
          </div>
        </div>
      </header>

      {/* 主要内容区域 */}
      <main className="relative z-10">
        <div className="mx-auto px-8 py-8" style={{ maxWidth: 'calc(100vw - 4rem)' }}>
          {renderContent()}
        </div>
      </main>

      {/* 背景装饰 */}
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-full blur-3xl animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-indigo-400/20 to-blue-400/20 rounded-full blur-3xl animate-float" style={{ animationDelay: '2s' }}></div>
      </div>
    </div>
  )
}

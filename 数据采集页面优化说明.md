# 数据采集子系统首页优化说明

## 优化概述

根据您的需求，我已经对 `web/apps/main/src/app/collection/page.tsx` 进行了全面优化，主要实现了以下功能：

## 主要改进

### 1. 全市24个数据来源单位展示
- ✅ 完整展示全市24个委办局的数据采集情况
- ✅ 每个单位都有专属图标和状态标识
- ✅ 实时显示各单位的数据采集状态

### 2. 详细的数据采集情况概览
- ✅ 每个单位显示数据源总数、在线数据源数量、数据总量
- ✅ 详细列出每个单位采集的具体数据类型
- ✅ 显示每种数据类型的记录数量和连接状态

### 3. 紧凑的页面布局优化
- ✅ 新增紧凑视图模式，一页可显示更多信息
- ✅ 支持标准视图和紧凑视图切换
- ✅ 响应式布局：紧凑模式下最多4列，标准模式3列

### 4. 强大的搜索和过滤功能
- ✅ 实时搜索数据来源单位
- ✅ 按状态过滤（正常运行、部分异常、连接异常）
- ✅ 显示过滤结果统计信息

## 数据来源单位列表

已完整配置24个数据来源单位：

1. **市政府办公厅** - 政务公开、会议纪要、公文流转数据
2. **发展改革委** - 项目投资、经济指标、规划、价格监测数据
3. **教育局** - 学生学籍、教师信息、学校基础、考试成绩、教育资源数据
4. **科技局** - 科技项目、专利申请、科技企业数据
5. **工信局** - 工业企业、产业园区、信息化项目、节能减排数据
6. **公安局** - 人口基础信息、户籍管理、身份证、治安管理、交通违法、案件管理数据
7. **民政局** - 婚姻登记、社会救助、养老服务、社会组织数据
8. **司法局** - 法律援助、公证服务、人民调解数据
9. **财政局** - 预算执行、政府采购、国有资产、财政收支、专项资金数据
10. **人社局** - 就业登记、社保缴费、人才信息、职业培训、劳动关系、工资指导数据
11. **自然资源局** - 土地利用、不动产登记、地理信息、矿产资源数据
12. **生态环境局** - 空气质量、水质监测、污染源、环评审批、生态保护数据
13. **住建局** - 房屋建筑、城市规划、建筑许可、物业管理数据
14. **交通运输局** - 道路运输、公共交通、交通基础设施、运输企业、交通违法数据
15. **水务局** - 供水管网、污水处理、水资源、防汛数据
16. **农业农村局** - 农业生产、农村土地、农产品质量、农业补贴数据
17. **商务局** - 对外贸易、招商引资、商贸流通数据
18. **文旅局** - 旅游景区、文化场馆、文物保护数据
19. **卫健委** - 医疗机构、医护人员、公共卫生、疫情防控、健康档案、医疗服务数据
20. **应急管理局** - 安全生产、应急预案、灾害监测、应急物资数据
21. **审计局** - 审计项目、审计结果、整改跟踪数据
22. **市场监管局** - 企业注册、食品安全、产品质量、价格监管、知识产权数据
23. **统计局** - 经济统计、人口统计、社会统计、专项调查数据
24. **医保局** - 医保参保、医保结算、定点医疗机构、药品目录数据

## 页面功能特性

### 总体统计面板
- 数据来源单位：24个（全覆盖）
- 数据源总数：156个（+12）
- 在线数据源：142个（91%）
- 今日采集量：2.8TB（+15%）

### 视图模式
1. **紧凑视图**：适合快速浏览，一页显示更多单位
2. **详细视图**：显示完整的数据类型列表和详细信息

### 交互功能
- 实时搜索单位名称
- 状态过滤（全部/正常/异常/部分异常）
- 视图模式切换
- 快速操作按钮（详情、同步、管理）

## 技术实现

### 响应式设计
- 移动端：1列布局
- 平板端：2列布局
- 桌面端：紧凑模式4列，标准模式3列

### 状态管理
- 使用React Hooks管理搜索、过滤、视图状态
- 实时过滤和搜索结果更新

### 用户体验
- 平滑的动画过渡效果
- 悬停状态反馈
- 清晰的状态指示器
- 直观的图标系统

## 使用说明

1. **查看概览**：页面顶部显示全市数据采集总体情况
2. **搜索单位**：使用搜索框快速找到特定的数据来源单位
3. **过滤状态**：选择状态过滤器查看特定状态的单位
4. **切换视图**：使用视图切换按钮在紧凑和详细模式间切换
5. **查看详情**：点击单位卡片上的操作按钮查看更多信息

这次优化大大提升了页面的信息密度和用户体验，让管理员能够更高效地监控全市数据采集情况。

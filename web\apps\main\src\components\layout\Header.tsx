'use client'

import { Bell, Search, User, ChevronDown } from 'lucide-react'

export function Header() {
  return (
    <header className="bg-white/80 backdrop-blur-xl shadow-glass border-b border-white/20 sticky top-0 z-40">
      <div className="flex items-center justify-between h-18 px-8">
        {/* 搜索栏 */}
        <div className="flex-1 max-w-2xl">
          <div className="relative group">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 group-focus-within:text-blue-500 transition-colors" />
            <input
              type="text"
              placeholder="搜索功能、数据、报表..."
              className="w-full pl-12 pr-6 py-4 bg-white/60 backdrop-blur-sm border border-gray-200/50 rounded-2xl focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500/50 focus:bg-white/80 transition-all duration-300 text-gray-700 placeholder-gray-400"
            />
            <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/5 to-indigo-500/5 opacity-0 group-focus-within:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
          </div>
        </div>

        {/* 右侧操作区 */}
        <div className="flex items-center space-x-6">
          {/* 快捷操作 */}
          <div className="hidden lg:flex items-center space-x-3">
            <button className="px-4 py-2 text-sm font-medium text-gray-600 hover:text-blue-600 bg-gray-50/50 hover:bg-blue-50/50 rounded-xl transition-all duration-300">
              快速导入
            </button>
            <button className="px-4 py-2 text-sm font-medium text-gray-600 hover:text-blue-600 bg-gray-50/50 hover:bg-blue-50/50 rounded-xl transition-all duration-300">
              数据导出
            </button>
          </div>

          {/* 通知 */}
          <button className="relative p-3 text-gray-400 hover:text-blue-600 bg-gray-50/50 hover:bg-blue-50/50 rounded-xl transition-all duration-300 group">
            <Bell className="w-5 h-5" />
            <span className="absolute top-2 right-2 w-2 h-2 bg-gradient-to-r from-red-500 to-pink-500 rounded-full animate-pulse"></span>
            <div className="absolute -top-1 -right-1 w-6 h-6 bg-red-500 text-white text-xs rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              3
            </div>
          </button>

          {/* 用户菜单 */}
          <div className="flex items-center space-x-3">
            <div className="flex items-center space-x-3 px-4 py-3 rounded-2xl bg-gradient-to-r from-gray-50/50 to-blue-50/30 hover:from-blue-50/50 hover:to-indigo-50/50 cursor-pointer transition-all duration-300 group">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-glow transition-all duration-300">
                <User className="w-5 h-5 text-white" />
              </div>
              <div className="hidden md:block">
                <div className="text-sm font-semibold text-gray-900 group-hover:text-blue-700 transition-colors">管理员</div>
                <div className="text-xs text-gray-500 group-hover:text-blue-500 transition-colors">系统管理员</div>
              </div>
              <ChevronDown className="w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors" />
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

'use client'

import { Shield, Users, UserCheck, Lock, Key, Eye, AlertTriangle, CheckCircle } from 'lucide-react'

export default function PermissionsPage() {
  const permissionStats = [
    {
      label: '注册用户',
      value: '156',
      trend: '+12',
      icon: Users,
      color: 'indigo'
    },
    {
      label: '活跃用户',
      value: '89',
      trend: '+8',
      icon: UserCheck,
      color: 'blue'
    },
    {
      label: '权限角色',
      value: '12',
      trend: '+2',
      icon: Shield,
      color: 'green'
    },
    {
      label: '访问规则',
      value: '45',
      trend: '+5',
      icon: Lock,
      color: 'purple'
    }
  ]

  const permissionCategories = [
    {
      id: 'users',
      name: '用户权限',
      icon: Users,
      count: 156,
      description: '管理用户账户和个人权限',
      color: 'indigo',
      features: ['用户管理', '权限分配', '账户状态', '登录记录']
    },
    {
      id: 'roles',
      name: '角色管理',
      icon: Shield,
      count: 12,
      description: '定义和管理用户角色',
      color: 'blue',
      features: ['角色定义', '权限组合', '角色分配', '继承关系']
    },
    {
      id: 'access',
      name: '访问控制',
      icon: Lock,
      count: 45,
      description: '配置资源访问控制规则',
      color: 'green',
      features: ['访问规则', '时间限制', 'IP限制', '审计日志']
    }
  ]

  const recentActivities = [
    {
      id: 1,
      type: 'user_login',
      user: '张三',
      action: '用户登录',
      resource: '人口数据库',
      time: '2分钟前',
      status: 'success',
      ip: '*************'
    },
    {
      id: 2,
      type: 'permission_grant',
      user: '李四',
      action: '权限授予',
      resource: '企业信息API',
      time: '5分钟前',
      status: 'success',
      ip: '*************'
    },
    {
      id: 3,
      type: 'access_denied',
      user: '王五',
      action: '访问被拒',
      resource: '地理信息数据',
      time: '10分钟前',
      status: 'failed',
      ip: '*************'
    },
    {
      id: 4,
      type: 'role_change',
      user: '赵六',
      action: '角色变更',
      resource: '数据管理员',
      time: '15分钟前',
      status: 'success',
      ip: '*************'
    }
  ]

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_login': return <UserCheck className="w-5 h-5 text-blue-500" />
      case 'permission_grant': return <Key className="w-5 h-5 text-green-500" />
      case 'access_denied': return <AlertTriangle className="w-5 h-5 text-red-500" />
      case 'role_change': return <Shield className="w-5 h-5 text-purple-500" />
      default: return <Eye className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'failed': return <AlertTriangle className="w-4 h-4 text-red-500" />
      default: return <AlertTriangle className="w-4 h-4 text-gray-500" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-green-100 text-green-700'
      case 'failed': return 'bg-red-100 text-red-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">权限控制</h1>
        <p className="text-xl text-gray-600">管理用户权限、角色和访问控制</p>
      </div>

      {/* 权限统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {permissionStats.map((stat) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-sm text-green-600 font-medium">{stat.trend}</p>
                </div>
                <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 权限管理分类 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {permissionCategories.map((category) => {
          const Icon = category.icon
          return (
            <div
              key={category.id}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 cursor-pointer"
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-br from-${category.color}-500 to-${category.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <span className="text-2xl font-bold text-gray-900">{category.count}</span>
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">{category.name}</h3>
              <p className="text-sm text-gray-600 mb-4">{category.description}</p>
              <div className="flex flex-wrap gap-2">
                {category.features.map((feature) => (
                  <span key={feature} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                    {feature}
                  </span>
                ))}
              </div>
            </div>
          )
        })}
      </div>

      {/* 安全状态概览 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <h3 className="text-lg font-bold text-gray-900 mb-4">安全状态概览</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mx-auto mb-3">
              <CheckCircle className="w-8 h-8 text-white" />
            </div>
            <p className="text-2xl font-bold text-gray-900">98.5%</p>
            <p className="text-sm text-gray-600">安全合规率</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Shield className="w-8 h-8 text-white" />
            </div>
            <p className="text-2xl font-bold text-gray-900">24/7</p>
            <p className="text-sm text-gray-600">安全监控</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-xl flex items-center justify-center mx-auto mb-3">
              <Lock className="w-8 h-8 text-white" />
            </div>
            <p className="text-2xl font-bold text-gray-900">0</p>
            <p className="text-sm text-gray-600">安全事件</p>
          </div>
        </div>
      </div>

      {/* 最近活动 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <h3 className="text-lg font-bold text-gray-900">最近权限活动</h3>
        </div>
        <div className="divide-y divide-gray-100">
          {recentActivities.map((activity) => (
            <div
              key={activity.id}
              className="p-6 hover:bg-gray-50/50 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 flex-1">
                  <div className="flex-shrink-0">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="text-lg font-medium text-gray-900">{activity.user}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(activity.status)}`}>
                        {activity.action}
                      </span>
                    </div>
                    <div className="flex items-center space-x-6 text-sm text-gray-500">
                      <span className="flex items-center space-x-1">
                        <Eye className="w-4 h-4" />
                        <span>{activity.resource}</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Shield className="w-4 h-4" />
                        <span>{activity.ip}</span>
                      </span>
                      <span>{activity.time}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(activity.status)}
                  <button className="text-indigo-600 hover:text-indigo-700 font-medium text-sm transition-colors">
                    详情
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

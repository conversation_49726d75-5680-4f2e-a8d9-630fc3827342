'use client'

import {
  Server,
  Database,
  Activity,
  Cpu,
  HardDrive,
  Network,
  AlertTriangle,
  CheckCircle,
  Clock,
  Settings,
  BarChart3,
  Zap,
  FileText,
  Layers
} from 'lucide-react'

export default function ChannelManagement() {
  const channels = [
    {
      id: 1,
      name: 'Kafka 集群',
      type: 'kafka',
      status: 'healthy',
      description: '高吞吐量消息队列，处理实时数据流',
      nodes: 3,
      throughput: '2.5K msg/s',
      storage: '1.2TB',
      uptime: '99.9%',
      cpu: 45,
      memory: 68,
      network: 32,
      partitions: 24,
      consumers: 8
    },
    {
      id: 2,
      name: 'RabbitMQ 集群',
      type: 'rabbitmq',
      status: 'healthy',
      description: '可靠消息传递，支持复杂路由',
      nodes: 2,
      throughput: '1.8K msg/s',
      storage: '450GB',
      uptime: '99.8%',
      cpu: 38,
      memory: 55,
      network: 28,
      queues: 16,
      exchanges: 12
    },
    {
      id: 3,
      name: 'Redis 缓存',
      type: 'redis',
      status: 'warning',
      description: '高性能内存数据库，用于缓存和会话存储',
      nodes: 2,
      throughput: '15K ops/s',
      storage: '128GB',
      uptime: '99.5%',
      cpu: 72,
      memory: 85,
      network: 45,
      keys: '2.4M',
      hitRate: '94.2%'
    },
    {
      id: 4,
      name: '中间库数据库',
      type: 'database',
      status: 'healthy',
      description: 'PostgreSQL 集群，存储处理后的结构化数据',
      nodes: 2,
      throughput: '850 TPS',
      storage: '5.6TB',
      uptime: '99.9%',
      cpu: 52,
      memory: 71,
      network: 35,
      connections: 245,
      tables: 156
    },
    {
      id: 5,
      name: 'FTP 文件服务',
      type: 'ftp',
      status: 'error',
      description: '文件传输服务，处理大文件和批量数据',
      nodes: 1,
      throughput: '120 MB/s',
      storage: '2.8TB',
      uptime: '98.2%',
      cpu: 25,
      memory: 42,
      network: 78,
      files: '45K',
      transfers: 156
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'warning': return <AlertTriangle className="w-5 h-5 text-yellow-500" />
      case 'error': return <AlertTriangle className="w-5 h-5 text-red-500" />
      default: return <Clock className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'healthy': return '健康'
      case 'warning': return '警告'
      case 'error': return '错误'
      default: return '未知'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-100 text-green-700'
      case 'warning': return 'bg-yellow-100 text-yellow-700'
      case 'error': return 'bg-red-100 text-red-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'kafka': return <Layers className="w-8 h-8 text-white" />
      case 'rabbitmq': return <Network className="w-8 h-8 text-white" />
      case 'redis': return <Zap className="w-8 h-8 text-white" />
      case 'database': return <Database className="w-8 h-8 text-white" />
      case 'ftp': return <FileText className="w-8 h-8 text-white" />
      default: return <Server className="w-8 h-8 text-white" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'kafka': return 'from-blue-500 to-indigo-500'
      case 'rabbitmq': return 'from-indigo-500 to-cyan-500'
      case 'redis': return 'from-red-500 to-pink-500'
      case 'database': return 'from-green-500 to-emerald-500'
      case 'ftp': return 'from-blue-500 to-indigo-500'
      default: return 'from-gray-500 to-slate-500'
    }
  }

  const overallStats = [
    { label: '总通道数', value: '5', icon: Server, color: 'blue' },
    { label: '健康通道', value: '3', icon: CheckCircle, color: 'green' },
    { label: '总吞吐量', value: '6.2K/s', icon: Activity, color: 'indigo' },
    { label: '平均可用性', value: '99.5%', icon: BarChart3, color: 'cyan' }
  ]

  return (
    <div>
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">通道管理中心</h1>
        <p className="text-xl text-gray-600">管理各缓冲通道的运行情况、性能指标、部署架构和集群状态</p>
      </div>

      {/* 总体统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {overallStats.map((stat) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 通道列表 */}
      <div className="space-y-4">
        {channels.map((channel) => (
          <div
            key={channel.id}
            className="bg-white/80 backdrop-blur-sm rounded-xl shadow-md border border-white/20 hover:shadow-lg transition-all duration-300"
          >
            <div className="p-4">
              {/* 头部 */}
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-4 flex-1">
                  <div className={`w-12 h-12 bg-gradient-to-br ${getTypeColor(channel.type)} rounded-xl flex items-center justify-center flex-shrink-0`}>
                    {getTypeIcon(channel.type)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-1">
                      <h3 className="text-lg font-bold text-gray-900">{channel.name}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(channel.status)}`}>
                        {getStatusText(channel.status)}
                      </span>
                      {getStatusIcon(channel.status)}
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{channel.description}</p>
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>节点: {channel.nodes}</span>
                      <span>可用性: {channel.uptime}</span>
                      <span>吞吐量: {channel.throughput}</span>
                      <span>存储: {channel.storage}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 性能指标 */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3 mb-4">
                <div className="bg-gray-50/50 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs text-gray-600 flex items-center space-x-1">
                      <Cpu className="w-3 h-3" />
                      <span>CPU</span>
                    </span>
                    <span className="text-xs font-medium">{channel.cpu}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div
                      className={`h-1.5 rounded-full transition-all duration-500 ${
                        channel.cpu > 80 ? 'bg-red-500' : channel.cpu > 60 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${channel.cpu}%` }}
                    ></div>
                  </div>
                </div>

                <div className="bg-gray-50/50 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs text-gray-600 flex items-center space-x-1">
                      <HardDrive className="w-3 h-3" />
                      <span>内存</span>
                    </span>
                    <span className="text-xs font-medium">{channel.memory}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div
                      className={`h-1.5 rounded-full transition-all duration-500 ${
                        channel.memory > 80 ? 'bg-red-500' : channel.memory > 60 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${channel.memory}%` }}
                    ></div>
                  </div>
                </div>

                <div className="bg-gray-50/50 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs text-gray-600 flex items-center space-x-1">
                      <Network className="w-3 h-3" />
                      <span>网络</span>
                    </span>
                    <span className="text-xs font-medium">{channel.network}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-1.5">
                    <div
                      className={`h-1.5 rounded-full transition-all duration-500 ${
                        channel.network > 80 ? 'bg-red-500' : channel.network > 60 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}
                      style={{ width: `${channel.network}%` }}
                    ></div>
                  </div>
                </div>
              </div>

              {/* 详细信息和操作 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-6 text-xs text-gray-600">
                  {channel.type === 'kafka' && (
                    <>
                      <span>分区: {channel.partitions}</span>
                      <span>消费者: {channel.consumers}</span>
                    </>
                  )}
                  {channel.type === 'rabbitmq' && (
                    <>
                      <span>队列: {channel.queues}</span>
                      <span>交换器: {channel.exchanges}</span>
                    </>
                  )}
                  {channel.type === 'redis' && (
                    <>
                      <span>键: {channel.keys}</span>
                      <span>命中率: {channel.hitRate}</span>
                    </>
                  )}
                  {channel.type === 'database' && (
                    <>
                      <span>连接: {channel.connections}</span>
                      <span>表: {channel.tables}</span>
                    </>
                  )}
                  {channel.type === 'ftp' && (
                    <>
                      <span>文件: {channel.files}</span>
                      <span>传输: {channel.transfers}</span>
                    </>
                  )}
                </div>

                {/* 操作按钮 */}
                <div className="flex items-center space-x-3">
                  <button className="text-blue-600 hover:text-blue-700 text-xs transition-colors flex items-center space-x-1">
                    <BarChart3 className="w-3 h-3" />
                    <span>监控</span>
                  </button>
                  <button className="text-green-600 hover:text-green-700 text-xs transition-colors flex items-center space-x-1">
                    <Settings className="w-3 h-3" />
                    <span>配置</span>
                  </button>
                  <button className="text-blue-600 hover:text-blue-700 text-xs transition-colors">
                    详情
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

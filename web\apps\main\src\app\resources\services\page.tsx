'use client'

import { Server, Database, Cpu, HardDrive, Activity, Users, Clock, CheckCircle } from 'lucide-react'

export default function ResourceServicesPage() {
  const serviceCategories = [
    {
      id: 'data',
      name: '数据服务',
      icon: Database,
      count: 12,
      description: '数据查询、分析和处理服务',
      color: 'indigo',
      services: ['数据查询', '数据分析', '数据清洗', '数据同步']
    },
    {
      id: 'compute',
      name: '计算服务',
      icon: Cpu,
      count: 8,
      description: '高性能计算和AI算法服务',
      color: 'blue',
      services: ['机器学习', '图像识别', '自然语言处理', '数据挖掘']
    },
    {
      id: 'storage',
      name: '存储服务',
      icon: HardDrive,
      count: 6,
      description: '文件存储和备份服务',
      color: 'green',
      services: ['文件存储', '数据备份', '归档服务', 'CDN加速']
    }
  ]

  const serviceStats = [
    {
      label: '运行服务',
      value: '26',
      trend: '+3',
      icon: Server,
      color: 'indigo'
    },
    {
      label: '活跃用户',
      value: '156',
      trend: '+12',
      icon: Users,
      color: 'blue'
    },
    {
      label: '服务调用',
      value: '12.4K',
      trend: '+15%',
      icon: Activity,
      color: 'green'
    },
    {
      label: '平均响应',
      value: '95ms',
      trend: '-8ms',
      icon: Clock,
      color: 'purple'
    }
  ]

  const recentServices = [
    {
      id: 1,
      name: '人口数据查询服务',
      category: 'data',
      status: 'running',
      users: 45,
      calls: 1234,
      uptime: '99.9%',
      lastUsed: '2分钟前'
    },
    {
      id: 2,
      name: '图像识别服务',
      category: 'compute',
      status: 'running',
      users: 23,
      calls: 567,
      uptime: '99.5%',
      lastUsed: '5分钟前'
    },
    {
      id: 3,
      name: '文件存储服务',
      category: 'storage',
      status: 'maintenance',
      users: 67,
      calls: 890,
      uptime: '98.8%',
      lastUsed: '1小时前'
    },
    {
      id: 4,
      name: '数据分析服务',
      category: 'data',
      status: 'running',
      users: 34,
      calls: 2345,
      uptime: '99.7%',
      lastUsed: '刚刚'
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running': return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'maintenance': return <Clock className="w-5 h-5 text-yellow-500" />
      case 'stopped': return <Server className="w-5 h-5 text-red-500" />
      default: return <Server className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'running': return '运行中'
      case 'maintenance': return '维护中'
      case 'stopped': return '已停止'
      default: return '未知'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-700'
      case 'maintenance': return 'bg-yellow-100 text-yellow-700'
      case 'stopped': return 'bg-red-100 text-red-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">资源服务</h1>
        <p className="text-xl text-gray-600">管理和监控各类资源服务</p>
      </div>

      {/* 服务统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {serviceStats.map((stat) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-sm text-green-600 font-medium">{stat.trend}</p>
                </div>
                <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 服务分类 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {serviceCategories.map((category) => {
          const Icon = category.icon
          return (
            <div
              key={category.id}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300 cursor-pointer"
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-br from-${category.color}-500 to-${category.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
                <span className="text-2xl font-bold text-gray-900">{category.count}</span>
              </div>
              <h3 className="text-lg font-bold text-gray-900 mb-2">{category.name}</h3>
              <p className="text-sm text-gray-600 mb-4">{category.description}</p>
              <div className="flex flex-wrap gap-2">
                {category.services.map((service) => (
                  <span key={service} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">
                    {service}
                  </span>
                ))}
              </div>
            </div>
          )
        })}
      </div>

      {/* 最近使用的服务 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <h3 className="text-lg font-bold text-gray-900">最近使用的服务</h3>
        </div>
        <div className="divide-y divide-gray-100">
          {recentServices.map((service) => (
            <div
              key={service.id}
              className="p-6 hover:bg-gray-50/50 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 flex-1">
                  <div className="flex-shrink-0">
                    <Server className="w-8 h-8 text-indigo-500" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="text-lg font-medium text-gray-900">{service.name}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(service.status)}`}>
                        {getStatusText(service.status)}
                      </span>
                    </div>
                    <div className="flex items-center space-x-6 text-sm text-gray-500">
                      <span className="flex items-center space-x-1">
                        <Users className="w-4 h-4" />
                        <span>{service.users} 用户</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Activity className="w-4 h-4" />
                        <span>{service.calls} 调用</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <CheckCircle className="w-4 h-4" />
                        <span>{service.uptime} 可用性</span>
                      </span>
                      <span className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>最后使用: {service.lastUsed}</span>
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(service.status)}
                  <button className="text-indigo-600 hover:text-indigo-700 font-medium text-sm transition-colors">
                    管理
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

'use client'

import { CheckCircle, AlertTriangle, XCircle, Clock } from 'lucide-react'

const systemServices = [
  { name: '数据采集服务', status: 'running', uptime: '99.9%' },
  { name: '数据处理引擎', status: 'running', uptime: '99.8%' },
  { name: '报表生成服务', status: 'warning', uptime: '98.5%' },
  { name: '监控告警服务', status: 'running', uptime: '99.9%' },
  { name: '用户认证服务', status: 'running', uptime: '99.7%' },
  { name: '文件存储服务', status: 'error', uptime: '95.2%' },
]

const recentActivities = [
  { time: '10:30', action: '数据采集任务完成', type: 'success' },
  { time: '10:15', action: '报表生成异常', type: 'warning' },
  { time: '09:45', action: '系统备份完成', type: 'success' },
  { time: '09:30', action: '用户登录异常', type: 'error' },
  { time: '09:00', action: '定时任务启动', type: 'info' },
]

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'running':
      return <CheckCircle className="w-4 h-4 text-success-500" />
    case 'warning':
      return <AlertTriangle className="w-4 h-4 text-warning-500" />
    case 'error':
      return <XCircle className="w-4 h-4 text-error-500" />
    default:
      return <Clock className="w-4 h-4 text-gray-400" />
  }
}

const getActivityIcon = (type: string) => {
  switch (type) {
    case 'success':
      return <CheckCircle className="w-3 h-3 text-success-500" />
    case 'warning':
      return <AlertTriangle className="w-3 h-3 text-warning-500" />
    case 'error':
      return <XCircle className="w-3 h-3 text-error-500" />
    default:
      return <Clock className="w-3 h-3 text-gray-400" />
  }
}

export function SystemStatus() {
  return (
    <div className="space-y-6">
      {/* 系统服务状态 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">系统服务状态</h3>
        </div>
        <div className="card-content">
          <div className="space-y-3">
            {systemServices.map((service) => (
              <div key={service.name} className="flex items-center justify-between py-2">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(service.status)}
                  <span className="text-sm font-medium text-gray-900">
                    {service.name}
                  </span>
                </div>
                <div className="flex items-center space-x-4">
                  <span className="text-xs text-gray-500">
                    运行时间: {service.uptime}
                  </span>
                  <span
                    className={`px-2 py-1 text-xs font-medium rounded-full ${
                      service.status === 'running'
                        ? 'bg-success-100 text-success-700'
                        : service.status === 'warning'
                        ? 'bg-warning-100 text-warning-700'
                        : 'bg-error-100 text-error-700'
                    }`}
                  >
                    {service.status === 'running' ? '正常' : 
                     service.status === 'warning' ? '警告' : '异常'}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 最近活动 */}
      <div className="card">
        <div className="card-header">
          <h3 className="text-lg font-semibold text-gray-900">最近活动</h3>
        </div>
        <div className="card-content">
          <div className="space-y-3">
            {recentActivities.map((activity, index) => (
              <div key={index} className="flex items-center space-x-3 py-2">
                {getActivityIcon(activity.type)}
                <span className="text-xs text-gray-500 w-12">{activity.time}</span>
                <span className="text-sm text-gray-900 flex-1">{activity.action}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/data-lifecycle/page",{

/***/ "(app-pages-browser)/./src/app/data-lifecycle/components/GovernanceModule.tsx":
/*!****************************************************************!*\
  !*** ./src/app/data-lifecycle/components/GovernanceModule.tsx ***!
  \****************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ GovernanceModule; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@14.2.30_react-dom@18.3.1_react@18.3.1__react@18.3.1/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileCheck,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileCheck,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/file-check.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileCheck,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/alert-triangle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileCheck,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileCheck,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileCheck,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/x-circle.js\");\n/* harmony import */ var _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertTriangle,CheckCircle,Clock,FileCheck,Settings,Shield,XCircle!=!lucide-react */ \"(app-pages-browser)/../../node_modules/.pnpm/lucide-react@0.294.0_react@18.3.1/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction GovernanceModule() {\n    var _leftMenuItems_find, _leftMenuItems_find1;\n    _s();\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"quality\");\n    // 左侧导航菜单项\n    const leftMenuItems = [\n        {\n            id: \"quality\",\n            name: \"质量管控\",\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n            description: \"数据质量检查与管控\"\n        },\n        {\n            id: \"cleaning\",\n            name: \"数据清洗\",\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n            description: \"数据清洗与标准化\"\n        },\n        {\n            id: \"compliance\",\n            name: \"合规管理\",\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            description: \"数据合规性检查\"\n        },\n        {\n            id: \"rules\",\n            name: \"规则配置\",\n            icon: _barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            description: \"治理规则配置管理\"\n        }\n    ];\n    // 数据质量统计\n    const qualityStats = {\n        totalRecords: \"2,456,789\",\n        qualityScore: 96.8,\n        cleanedRecords: \"2,378,234\",\n        errorRecords: \"78,555\",\n        duplicateRecords: \"12,345\"\n    };\n    // 质量检查结果\n    const qualityChecks = [\n        {\n            id: 1,\n            name: \"人口信息完整性检查\",\n            status: \"passed\",\n            score: 98.5,\n            issues: 156,\n            lastCheck: \"2024-01-20 14:30:00\"\n        },\n        {\n            id: 2,\n            name: \"企业信息准确性检查\",\n            status: \"warning\",\n            score: 94.2,\n            issues: 1234,\n            lastCheck: \"2024-01-20 14:25:00\"\n        },\n        {\n            id: 3,\n            name: \"地理信息一致性检查\",\n            status: \"failed\",\n            score: 87.6,\n            issues: 2567,\n            lastCheck: \"2024-01-20 14:20:00\"\n        }\n    ];\n    // 清洗任务\n    const cleaningTasks = [\n        {\n            id: 1,\n            name: \"重复数据清理\",\n            type: \"deduplication\",\n            status: \"running\",\n            progress: 75,\n            processed: \"1,234,567\",\n            cleaned: \"12,345\"\n        },\n        {\n            id: 2,\n            name: \"格式标准化\",\n            type: \"standardization\",\n            status: \"completed\",\n            progress: 100,\n            processed: \"2,456,789\",\n            cleaned: \"245,678\"\n        },\n        {\n            id: 3,\n            name: \"空值填充\",\n            type: \"completion\",\n            status: \"pending\",\n            progress: 0,\n            processed: \"0\",\n            cleaned: \"0\"\n        }\n    ];\n    const getStatusIcon = (status)=>{\n        switch(status){\n            case \"passed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 29\n                }, this);\n            case \"warning\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-4 h-4 text-yellow-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 30\n                }, this);\n            case \"failed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"w-4 h-4 text-red-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 29\n                }, this);\n            case \"running\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 30\n                }, this);\n            case \"completed\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-4 h-4 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 32\n                }, this);\n            case \"pending\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 30\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                    className: \"w-4 h-4 text-gray-500\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"passed\":\n                return \"bg-green-100 text-green-700 border-green-200\";\n            case \"warning\":\n                return \"bg-yellow-100 text-yellow-700 border-yellow-200\";\n            case \"failed\":\n                return \"bg-red-100 text-red-700 border-red-200\";\n            case \"running\":\n                return \"bg-blue-100 text-blue-700 border-blue-200\";\n            case \"completed\":\n                return \"bg-green-100 text-green-700 border-green-200\";\n            case \"pending\":\n                return \"bg-gray-100 text-gray-700 border-gray-200\";\n            default:\n                return \"bg-gray-100 text-gray-700 border-gray-200\";\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case \"passed\":\n                return \"通过\";\n            case \"warning\":\n                return \"警告\";\n            case \"failed\":\n                return \"失败\";\n            case \"running\":\n                return \"运行中\";\n            case \"completed\":\n                return \"已完成\";\n            case \"pending\":\n                return \"待处理\";\n            default:\n                return \"未知\";\n        }\n    };\n    const getScoreColor = (score)=>{\n        if (score >= 95) return \"text-green-600\";\n        if (score >= 90) return \"text-yellow-600\";\n        return \"text-red-600\";\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-64 bg-white/80 backdrop-blur-sm border-r border-white/20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                children: \"数据治理功能\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"space-y-2\",\n                                children: leftMenuItems.map((item)=>{\n                                    const Icon = item.icon;\n                                    const isActive = activeTab === item.id;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setActiveTab(item.id),\n                                        className: \"w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 text-left \".concat(isActive ? \"bg-red-50 text-red-600 border border-red-200\" : \"text-gray-600 hover:text-red-600 hover:bg-gray-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                                                className: \"w-5 h-5\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: item.name\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-gray-500\",\n                                                        children: item.description\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this);\n                                })\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                        lineNumber: 151,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-6 border-t border-gray-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-semibold text-gray-900 mb-3\",\n                                children: \"治理状态\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"质量评分\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium \".concat(getScoreColor(qualityStats.qualityScore)),\n                                                children: [\n                                                    qualityStats.qualityScore,\n                                                    \"%\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"已清洗\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                lineNumber: 189,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-green-600\",\n                                                children: qualityStats.cleanedRecords\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                        lineNumber: 188,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: \"异常记录\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium text-red-600\",\n                                                children: qualityStats.errorRecords\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                lineNumber: 150,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 p-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl font-bold text-gray-900 mb-2\",\n                                    children: (_leftMenuItems_find = leftMenuItems.find((item)=>item.id === activeTab)) === null || _leftMenuItems_find === void 0 ? void 0 : _leftMenuItems_find.name\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                    lineNumber: 205,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: (_leftMenuItems_find1 = leftMenuItems.find((item)=>item.id === activeTab)) === null || _leftMenuItems_find1 === void 0 ? void 0 : _leftMenuItems_find1.description\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                    lineNumber: 208,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                            lineNumber: 204,\n                            columnNumber: 11\n                        }, this),\n                        activeTab === \"quality\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                            children: \"数据质量概览\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                            lineNumber: 218,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-blue-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-blue-600 mb-1\",\n                                                            children: qualityStats.totalRecords\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 221,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-blue-700\",\n                                                            children: \"总记录数\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 222,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 220,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-green-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold mb-1 \".concat(getScoreColor(qualityStats.qualityScore)),\n                                                            children: [\n                                                                qualityStats.qualityScore,\n                                                                \"%\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 225,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-green-700\",\n                                                            children: \"质量评分\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 228,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-green-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-green-600 mb-1\",\n                                                            children: qualityStats.cleanedRecords\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 231,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-green-700\",\n                                                            children: \"已清洗\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 232,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 230,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-red-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-red-600 mb-1\",\n                                                            children: qualityStats.errorRecords\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 235,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-red-700\",\n                                                            children: \"异常记录\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 236,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 234,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-yellow-50 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-2xl font-bold text-yellow-600 mb-1\",\n                                                            children: qualityStats.duplicateRecords\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 239,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-yellow-700\",\n                                                            children: \"重复记录\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 240,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 238,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                    lineNumber: 217,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                            children: \"质量检查结果\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: qualityChecks.map((check)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-6 bg-gray-50 rounded-lg border border-gray-200\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between mb-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center space-x-3\",\n                                                                    children: [\n                                                                        getStatusIcon(check.status),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                            className: \"font-semibold text-gray-900\",\n                                                                            children: check.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                            lineNumber: 254,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"px-2 py-1 rounded-full text-xs border \".concat(getStatusColor(check.status)),\n                                                                            children: getStatusText(check.status)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                            lineNumber: 255,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                    lineNumber: 252,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: check.lastCheck\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                    lineNumber: 259,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 251,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"质量评分：\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                            lineNumber: 265,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium \".concat(getScoreColor(check.score)),\n                                                                            children: [\n                                                                                check.score,\n                                                                                \"%\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                            lineNumber: 266,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"发现问题：\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                            lineNumber: 269,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium text-red-600\",\n                                                                            children: check.issues\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                            lineNumber: 270,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                    lineNumber: 268,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-sm text-gray-600\",\n                                                                            children: \"最后检查：\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                            lineNumber: 273,\n                                                                            columnNumber: 27\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"font-medium\",\n                                                                            children: check.lastCheck\n                                                                        }, void 0, false, {\n                                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                            lineNumber: 274,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                    lineNumber: 272,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, check.id, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 250,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                    lineNumber: 246,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === \"cleaning\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-semibold text-gray-900\",\n                                            children: \"数据清洗任务\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                            lineNumber: 287,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors\",\n                                            children: \"新建清洗任务\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                            lineNumber: 288,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                    lineNumber: 286,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: cleaningTasks.map((task)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 bg-gray-50 rounded-lg border border-gray-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3\",\n                                                        children: [\n                                                            getStatusIcon(task.status),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-semibold text-gray-900\",\n                                                                children: task.name\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"px-2 py-1 rounded-full text-xs border \".concat(getStatusColor(task.status)),\n                                                                children: getStatusText(task.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 295,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"已处理：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                    lineNumber: 306,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: task.processed\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                    lineNumber: 307,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 305,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"已清洗：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                    lineNumber: 310,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: task.cleaned\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                    lineNumber: 311,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-sm text-gray-600\",\n                                                                    children: \"类型：\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                    lineNumber: 314,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: task.type\n                                                                }, void 0, false, {\n                                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                                    lineNumber: 315,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 313,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 304,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-full bg-gray-200 rounded-full h-2\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-red-500 h-2 rounded-full transition-all duration-300\",\n                                                        style: {\n                                                            width: \"\".concat(task.progress, \"%\")\n                                                        }\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-right text-sm text-gray-600 mt-1\",\n                                                    children: [\n                                                        task.progress,\n                                                        \"%\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 324,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, task.id, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                    lineNumber: 292,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                            lineNumber: 285,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === \"compliance\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                    children: \"合规性检查\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                    lineNumber: 335,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-green-50 rounded-lg border border-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 339,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-green-700\",\n                                                            children: \"数据安全合规\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 340,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 338,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-green-600\",\n                                                    children: \"符合数据安全法规要求\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                            lineNumber: 337,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-green-50 rounded-lg border border-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-green-700\",\n                                                            children: \"隐私保护合规\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 347,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-green-600\",\n                                                    children: \"符合个人信息保护要求\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 349,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-yellow-50 rounded-lg border border-yellow-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                            className: \"w-5 h-5 text-yellow-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 353,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-yellow-700\",\n                                                            children: \"数据质量合规\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 354,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 352,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-yellow-600\",\n                                                    children: \"部分数据质量需要改进\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 356,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                            lineNumber: 351,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-green-50 rounded-lg border border-green-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center space-x-2 mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertTriangle_CheckCircle_Clock_FileCheck_Settings_Shield_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                            className: \"w-5 h-5 text-green-500\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium text-green-700\",\n                                                            children: \"访问控制合规\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 361,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-green-600\",\n                                                    children: \"访问权限控制符合要求\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                    lineNumber: 336,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                            lineNumber: 334,\n                            columnNumber: 13\n                        }, this),\n                        activeTab === \"rules\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/80 backdrop-blur-sm rounded-2xl p-8 shadow-lg border border-white/20\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-xl font-semibold text-gray-900 mb-6\",\n                                    children: \"治理规则配置\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-3\",\n                                                    children: \"数据完整性规则\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 必填字段不能为空\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 376,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 关键字段格式必须正确\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 外键关联必须有效\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-3\",\n                                                    children: \"数据准确性规则\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 382,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 数值范围必须合理\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 384,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 日期格式必须标准\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 385,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 枚举值必须在允许范围内\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 386,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                            lineNumber: 381,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-4 bg-gray-50 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium text-gray-900 mb-3\",\n                                                    children: \"数据一致性规则\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 跨表数据必须一致\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 392,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 业务逻辑必须正确\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            children: \"• 时间序列必须合理\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                            lineNumber: 370,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                    lineNumber: 202,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n                lineNumber: 201,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Workspace\\\\yy-zs\\\\web\\\\apps\\\\main\\\\src\\\\app\\\\data-lifecycle\\\\components\\\\GovernanceModule.tsx\",\n        lineNumber: 148,\n        columnNumber: 5\n    }, this);\n}\n_s(GovernanceModule, \"B9y6tiJjAARqnNhgJB4ygCGcsSQ=\");\n_c = GovernanceModule;\nvar _c;\n$RefreshReg$(_c, \"GovernanceModule\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/data-lifecycle/components/GovernanceModule.tsx\n"));

/***/ })

});
export default function ResourcesPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">资源池管理</h1>
          <p className="mt-1 text-gray-600">数据资源统一管理与权限控制</p>
        </div>
        <button className="btn-primary">
          添加资源
        </button>
      </div>

      {/* 资源统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[
          { label: '总存储容量', value: '10', unit: 'TB' },
          { label: '已使用容量', value: '2.4', unit: 'TB' },
          { label: '数据表总数', value: '156', unit: '张' },
          { label: 'API接口数', value: '48', unit: '个' },
        ].map((stat) => (
          <div key={stat.label} className="card">
            <div className="card-content text-center">
              <div className="text-2xl font-bold text-primary-600">
                {stat.value}
                <span className="text-sm text-gray-500 ml-1">{stat.unit}</span>
              </div>
              <div className="text-sm text-gray-600 mt-1">{stat.label}</div>
            </div>
          </div>
        ))}
      </div>

      {/* 资源目录 */}
      <div className="card">
        <div className="card-header">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">资源目录</h3>
            <div className="flex space-x-2">
              <select className="text-sm border border-gray-300 rounded px-3 py-1">
                <option>全部类型</option>
                <option>数据库表</option>
                <option>文件资源</option>
                <option>API接口</option>
              </select>
              <select className="text-sm border border-gray-300 rounded px-3 py-1">
                <option>全部部门</option>
                <option>政务服务</option>
                <option>民政局</option>
                <option>统计局</option>
              </select>
            </div>
          </div>
        </div>
        <div className="card-content">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">资源名称</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">类型</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">所属部门</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">大小</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">更新时间</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">权限</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">操作</th>
                </tr>
              </thead>
              <tbody>
                {[
                  { name: '人口基础信息表', type: '数据库表', dept: '民政局', size: '1.2GB', time: '2024-01-15', permission: '公开' },
                  { name: '企业注册信息', type: '数据库表', dept: '市场监管局', size: '856MB', time: '2024-01-14', permission: '受限' },
                  { name: '政务服务API', type: 'API接口', dept: '政务服务', size: '-', time: '2024-01-13', permission: '公开' },
                  { name: '统计年鉴数据', type: '文件资源', dept: '统计局', size: '245MB', time: '2024-01-12', permission: '内部' },
                ].map((resource, index) => (
                  <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-3 px-4 font-medium text-gray-900">{resource.name}</td>
                    <td className="py-3 px-4 text-gray-600">{resource.type}</td>
                    <td className="py-3 px-4 text-gray-600">{resource.dept}</td>
                    <td className="py-3 px-4 text-gray-600">{resource.size}</td>
                    <td className="py-3 px-4 text-gray-600">{resource.time}</td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        resource.permission === '公开' ? 'bg-success-100 text-success-700' :
                        resource.permission === '受限' ? 'bg-warning-100 text-warning-700' :
                        'bg-gray-100 text-gray-700'
                      }`}>
                        {resource.permission}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex space-x-2">
                        <button className="text-primary-600 hover:text-primary-700 text-sm">查看</button>
                        <button className="text-gray-600 hover:text-gray-700 text-sm">权限</button>
                        <button className="text-gray-600 hover:text-gray-700 text-sm">下载</button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}

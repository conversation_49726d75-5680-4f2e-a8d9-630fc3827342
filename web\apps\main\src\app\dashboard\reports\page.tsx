export default function ReportsPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">报表分析</h1>
          <p className="mt-1 text-gray-600">多维度数据分析报表</p>
        </div>
        <button className="btn-primary">
          新建报表
        </button>
      </div>

      {/* 报表分类 */}
      <div className="flex space-x-4 border-b border-gray-200">
        {['全部', '业务报表', '统计报表', '监控报表', '自定义报表'].map((tab) => (
          <button
            key={tab}
            className="px-4 py-2 text-sm font-medium text-gray-600 hover:text-primary-600 border-b-2 border-transparent hover:border-primary-600"
          >
            {tab}
          </button>
        ))}
      </div>

      {/* 报表列表 */}
      <div className="card">
        <div className="card-content">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-900">报表名称</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">类型</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">创建时间</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">状态</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-900">操作</th>
                </tr>
              </thead>
              <tbody>
                {[
                  { name: '政务服务月度统计', type: '业务报表', date: '2024-01-15', status: '正常' },
                  { name: '数据质量分析报告', type: '监控报表', date: '2024-01-14', status: '正常' },
                  { name: '系统性能监控', type: '监控报表', date: '2024-01-13', status: '异常' },
                  { name: '用户访问统计', type: '统计报表', date: '2024-01-12', status: '正常' },
                ].map((report, index) => (
                  <tr key={index} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-3 px-4 text-gray-900">{report.name}</td>
                    <td className="py-3 px-4 text-gray-600">{report.type}</td>
                    <td className="py-3 px-4 text-gray-600">{report.date}</td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        report.status === '正常' 
                          ? 'bg-success-100 text-success-700' 
                          : 'bg-error-100 text-error-700'
                      }`}>
                        {report.status}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex space-x-2">
                        <button className="text-primary-600 hover:text-primary-700 text-sm">查看</button>
                        <button className="text-gray-600 hover:text-gray-700 text-sm">编辑</button>
                        <button className="text-error-600 hover:text-error-700 text-sm">删除</button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}

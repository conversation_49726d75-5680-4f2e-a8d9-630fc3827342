'use client'

import { Globe, Zap, Shield, Clock, CheckCircle, AlertTriangle, Activity, Code } from 'lucide-react'

export default function APIsDirectoryPage() {
  const apiStats = [
    {
      label: 'API总数',
      value: '34',
      trend: '+5',
      icon: Globe,
      color: 'indigo'
    },
    {
      label: '在线服务',
      value: '32',
      trend: '+2',
      icon: CheckCircle,
      color: 'green'
    },
    {
      label: '今日调用',
      value: '12.4K',
      trend: '+15%',
      icon: Activity,
      color: 'blue'
    },
    {
      label: '平均响应',
      value: '120ms',
      trend: '-5ms',
      icon: Zap,
      color: 'yellow'
    }
  ]

  const apis = [
    {
      id: 1,
      name: '人口信息查询API',
      description: '根据身份证号查询人口基础信息',
      endpoint: '/api/v1/population/query',
      method: 'POST',
      department: '公安局',
      version: 'v1.2.0',
      status: 'online',
      responseTime: '95ms',
      dailyCalls: 1234,
      rateLimit: '1000/hour',
      authentication: 'API Key + OAuth2',
      lastUpdated: '2024-01-15'
    },
    {
      id: 2,
      name: '企业信息查询API',
      description: '查询企业注册信息、经营状态等',
      endpoint: '/api/v1/enterprise/info',
      method: 'GET',
      department: '市场监管局',
      version: 'v2.1.0',
      status: 'online',
      responseTime: '156ms',
      dailyCalls: 567,
      rateLimit: '500/hour',
      authentication: 'API Key',
      lastUpdated: '2024-01-14'
    },
    {
      id: 3,
      name: '地理编码服务API',
      description: '地址转换为经纬度坐标',
      endpoint: '/api/v1/geocoding/address',
      method: 'GET',
      department: '自然资源局',
      version: 'v1.0.0',
      status: 'maintenance',
      responseTime: '234ms',
      dailyCalls: 89,
      rateLimit: '200/hour',
      authentication: 'API Key',
      lastUpdated: '2024-01-13'
    },
    {
      id: 4,
      name: '环境数据监测API',
      description: '实时获取环境监测数据',
      endpoint: '/api/v1/environment/monitor',
      method: 'GET',
      department: '环保局',
      version: 'v1.1.0',
      status: 'online',
      responseTime: '78ms',
      dailyCalls: 2345,
      rateLimit: '2000/hour',
      authentication: 'Bearer Token',
      lastUpdated: '2024-01-12'
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <CheckCircle className="w-5 h-5 text-green-500" />
      case 'maintenance': return <AlertTriangle className="w-5 h-5 text-yellow-500" />
      case 'offline': return <AlertTriangle className="w-5 h-5 text-red-500" />
      default: return <AlertTriangle className="w-5 h-5 text-gray-500" />
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'online': return '在线'
      case 'maintenance': return '维护中'
      case 'offline': return '离线'
      default: return '未知'
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'bg-green-100 text-green-700'
      case 'maintenance': return 'bg-yellow-100 text-yellow-700'
      case 'offline': return 'bg-red-100 text-red-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'bg-blue-100 text-blue-700'
      case 'POST': return 'bg-green-100 text-green-700'
      case 'PUT': return 'bg-yellow-100 text-yellow-700'
      case 'DELETE': return 'bg-red-100 text-red-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">接口目录</h1>
        <p className="text-xl text-gray-600">浏览和管理API接口服务</p>
      </div>

      {/* API统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {apiStats.map((stat) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-sm text-green-600 font-medium">{stat.trend}</p>
                </div>
                <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* API服务状态 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <h3 className="text-lg font-bold text-gray-900 mb-4">服务状态概览</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mx-auto mb-3">
              <CheckCircle className="w-8 h-8 text-white" />
            </div>
            <p className="text-2xl font-bold text-gray-900">32</p>
            <p className="text-sm text-gray-600">正常运行</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center mx-auto mb-3">
              <AlertTriangle className="w-8 h-8 text-white" />
            </div>
            <p className="text-2xl font-bold text-gray-900">1</p>
            <p className="text-sm text-gray-600">维护中</p>
          </div>
          <div className="text-center">
            <div className="w-16 h-16 bg-gradient-to-br from-red-500 to-pink-500 rounded-xl flex items-center justify-center mx-auto mb-3">
              <AlertTriangle className="w-8 h-8 text-white" />
            </div>
            <p className="text-2xl font-bold text-gray-900">1</p>
            <p className="text-sm text-gray-600">离线</p>
          </div>
        </div>
      </div>

      {/* API列表 */}
      <div className="space-y-6">
        {apis.map((api) => (
          <div
            key={api.id}
            className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden"
          >
            {/* API头部信息 */}
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h3 className="text-xl font-bold text-gray-900">{api.name}</h3>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(api.status)}`}>
                      {getStatusText(api.status)}
                    </span>
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${getMethodColor(api.method)}`}>
                      {api.method}
                    </span>
                  </div>
                  <p className="text-gray-600 mb-4">{api.description}</p>
                  <div className="flex items-center space-x-6 text-sm text-gray-600">
                    <span className="flex items-center space-x-1">
                      <Code className="w-4 h-4" />
                      <span>{api.endpoint}</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <Globe className="w-4 h-4" />
                      <span>{api.version}</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <Shield className="w-4 h-4" />
                      <span>{api.department}</span>
                    </span>
                    <span className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>更新: {api.lastUpdated}</span>
                    </span>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(api.status)}
                  <div className="w-16 h-16 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-xl flex items-center justify-center">
                    <Globe className="w-8 h-8 text-white" />
                  </div>
                </div>
              </div>
            </div>

            {/* API详细信息 */}
            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-gray-50/50 rounded-xl p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Zap className="w-5 h-5 text-yellow-500" />
                    <h5 className="font-medium text-gray-900">响应时间</h5>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{api.responseTime}</p>
                </div>
                <div className="bg-gray-50/50 rounded-xl p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Activity className="w-5 h-5 text-blue-500" />
                    <h5 className="font-medium text-gray-900">今日调用</h5>
                  </div>
                  <p className="text-2xl font-bold text-gray-900">{api.dailyCalls}</p>
                </div>
                <div className="bg-gray-50/50 rounded-xl p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Shield className="w-5 h-5 text-green-500" />
                    <h5 className="font-medium text-gray-900">限流规则</h5>
                  </div>
                  <p className="text-lg font-bold text-gray-900">{api.rateLimit}</p>
                </div>
                <div className="bg-gray-50/50 rounded-xl p-4">
                  <div className="flex items-center space-x-2 mb-2">
                    <Shield className="w-5 h-5 text-purple-500" />
                    <h5 className="font-medium text-gray-900">认证方式</h5>
                  </div>
                  <p className="text-sm font-medium text-gray-900">{api.authentication}</p>
                </div>
              </div>
            </div>

            {/* 操作按钮 */}
            <div className="px-6 py-4 bg-gray-50/30 border-t border-gray-100">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <button className="text-indigo-600 hover:text-indigo-700 font-medium text-sm transition-colors">
                    查看文档
                  </button>
                  <button className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors">
                    在线测试
                  </button>
                  <button className="text-green-600 hover:text-green-700 font-medium text-sm transition-colors">
                    获取密钥
                  </button>
                </div>
                <button className="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors text-sm font-medium">
                  申请调用
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

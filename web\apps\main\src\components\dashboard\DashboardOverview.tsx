'use client'

import { Database, Users, Activity, TrendingUp } from 'lucide-react'

const stats = [
  {
    name: '数据总量',
    value: '2.4TB',
    change: '+12.5%',
    changeType: 'positive',
    icon: Database,
    gradient: 'from-blue-500 to-cyan-500',
    description: '存储数据总量',
  },
  {
    name: '在线用户',
    value: '1,234',
    change: '+4.3%',
    changeType: 'positive',
    icon: Users,
    gradient: 'from-green-500 to-emerald-500',
    description: '当前活跃用户',
  },
  {
    name: '系统负载',
    value: '68%',
    change: '-2.1%',
    changeType: 'negative',
    icon: Activity,
    gradient: 'from-orange-500 to-red-500',
    description: '服务器负载率',
  },
  {
    name: '处理效率',
    value: '94.2%',
    change: '+1.8%',
    changeType: 'positive',
    icon: TrendingUp,
    gradient: 'from-purple-500 to-pink-500',
    description: '数据处理效率',
  },
]

export function DashboardOverview() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stats.map((stat, index) => {
        const Icon = stat.icon
        return (
          <div
            key={stat.name}
            className="card hover:shadow-glow transition-all duration-500 group animate-slide-up"
            style={{ animationDelay: `${index * 150}ms` }}
          >
            <div className="card-content relative overflow-hidden">
              {/* 背景渐变效果 */}
              <div className={`absolute inset-0 bg-gradient-to-br ${stat.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}></div>

              <div className="relative z-10">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <p className="text-sm font-medium text-gray-500 mb-1">{stat.description}</p>
                    <p className="text-sm font-semibold text-gray-700 mb-2">{stat.name}</p>
                    <p className="text-3xl font-bold text-gray-900 group-hover:text-blue-700 transition-colors">
                      {stat.value}
                    </p>
                  </div>
                  <div className={`p-4 bg-gradient-to-br ${stat.gradient} rounded-2xl shadow-lg group-hover:shadow-glow transition-all duration-300 transform group-hover:scale-110`}>
                    <Icon className="w-6 h-6 text-white" />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span
                      className={`text-sm font-bold px-2 py-1 rounded-full ${
                        stat.changeType === 'positive'
                          ? 'text-green-700 bg-green-100'
                          : 'text-red-700 bg-red-100'
                      }`}
                    >
                      {stat.change}
                    </span>
                    <span className="text-xs text-gray-500">较上月</span>
                  </div>

                  {/* 趋势指示器 */}
                  <div className="flex items-center space-x-1">
                    {[...Array(3)].map((_, i) => (
                      <div
                        key={i}
                        className={`w-1 h-4 rounded-full ${
                          stat.changeType === 'positive' ? 'bg-green-400' : 'bg-red-400'
                        } animate-pulse`}
                        style={{
                          animationDelay: `${i * 200}ms`,
                          height: `${12 + i * 4}px`
                        }}
                      ></div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 悬浮时的光效 */}
              <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-500">
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-white/50 to-transparent transform -translate-x-full group-hover:translate-x-full transition-transform duration-1000"></div>
              </div>
            </div>
          </div>
        )
      })}
    </div>
  )
}

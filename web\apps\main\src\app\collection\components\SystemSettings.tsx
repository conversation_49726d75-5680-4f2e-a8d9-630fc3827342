'use client'

export default function SystemSettings() {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900">系统配置</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-white/80 rounded-lg p-4 border border-white/20">
          <h4 className="font-semibold text-gray-900 mb-2">采集配置</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">默认采集频率:</span>
              <span className="text-gray-900">每小时</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">数据保留期:</span>
              <span className="text-gray-900">365天</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">最大并发连接:</span>
              <span className="text-gray-900">50</span>
            </div>
          </div>
        </div>
        <div className="bg-white/80 rounded-lg p-4 border border-white/20">
          <h4 className="font-semibold text-gray-900 mb-2">Agent配置</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">心跳间隔:</span>
              <span className="text-gray-900">30秒</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">超时时间:</span>
              <span className="text-gray-900">5分钟</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">重试次数:</span>
              <span className="text-gray-900">3次</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

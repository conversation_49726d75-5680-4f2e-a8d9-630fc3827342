'use client'

import Link from 'next/link'
import { 
  Shield, 
  CheckCircle, 
  AlertTriangle, 
  XCircle,
  TrendingUp,
  Database,
  FileCheck,
  Users,
  Settings,
  Plus,
  ArrowRight,
  BarChart3,
  Clock
} from 'lucide-react'

export default function GovernanceSystemPage() {
  const qualityMetrics = [
    {
      name: '数据完整性',
      score: 92,
      trend: '+3%',
      status: 'good',
      issues: 12,
      category: '质量指标'
    },
    {
      name: '数据准确性',
      score: 88,
      trend: '+1%',
      status: 'good',
      issues: 8,
      category: '质量指标'
    },
    {
      name: '数据一致性',
      score: 76,
      trend: '-2%',
      status: 'warning',
      issues: 24,
      category: '质量指标'
    },
    {
      name: '数据时效性',
      score: 94,
      trend: '+5%',
      status: 'good',
      issues: 3,
      category: '质量指标'
    }
  ]

  const governanceRules = [
    {
      id: 1,
      name: '个人信息脱敏规则',
      description: '对个人敏感信息进行自动脱敏处理',
      type: '数据安全',
      status: 'active',
      coverage: '100%',
      lastUpdated: '2024-01-15'
    },
    {
      id: 2,
      name: '数据质量检查规则',
      description: '检查数据完整性、准确性和一致性',
      type: '质量控制',
      status: 'active',
      coverage: '95%',
      lastUpdated: '2024-01-14'
    },
    {
      id: 3,
      name: '数据访问权限规则',
      description: '控制不同用户对数据的访问权限',
      type: '权限管理',
      status: 'review',
      coverage: '88%',
      lastUpdated: '2024-01-10'
    }
  ]

  const quickStats = [
    { label: '数据质量评分', value: '92', trend: '+2%', icon: Shield, color: 'blue' },
    { label: '治理规则', value: '24', trend: '+3', icon: FileCheck, color: 'indigo' },
    { label: '合规检查', value: '156', trend: '+12', icon: CheckCircle, color: 'green' },
    { label: '问题修复', value: '89%', trend: '+5%', icon: TrendingUp, color: 'cyan' }
  ]

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 80) return 'text-yellow-600'
    return 'text-red-600'
  }

  const getScoreBg = (score: number) => {
    if (score >= 90) return 'bg-green-500'
    if (score >= 80) return 'bg-yellow-500'
    return 'bg-red-500'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-700'
      case 'review': return 'bg-yellow-100 text-yellow-700'
      case 'inactive': return 'bg-gray-100 text-gray-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '生效中'
      case 'review': return '审核中'
      case 'inactive': return '已停用'
      default: return '未知'
    }
  }

  return (
    <div className="max-w-7xl mx-auto px-6 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">数据治理管理中心</h1>
        <p className="text-xl text-gray-600">监控数据质量，管理治理规则，确保数据合规</p>
      </div>

      {/* 快速统计 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {quickStats.map((stat, index) => {
          const Icon = stat.icon
          return (
            <div
              key={stat.label}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                  <p className="text-3xl font-bold text-gray-900">{stat.value}</p>
                  <p className="text-sm text-green-600 font-medium">{stat.trend}</p>
                </div>
                <div className={`w-12 h-12 bg-gradient-to-br from-${stat.color}-500 to-${stat.color}-600 rounded-xl flex items-center justify-center`}>
                  <Icon className="w-6 h-6 text-white" />
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* 数据质量指标 */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">数据质量指标</h2>
          <button className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors">
            查看详细报告
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {qualityMetrics.map((metric, index) => (
            <div
              key={metric.name}
              className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="text-center">
                <div className="relative w-20 h-20 mx-auto mb-4">
                  <svg className="w-20 h-20 transform -rotate-90" viewBox="0 0 36 36">
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke="#e5e7eb"
                      strokeWidth="2"
                    />
                    <path
                      d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"
                      fill="none"
                      stroke={metric.score >= 90 ? '#10b981' : metric.score >= 80 ? '#f59e0b' : '#ef4444'}
                      strokeWidth="2"
                      strokeDasharray={`${metric.score}, 100`}
                    />
                  </svg>
                  <div className="absolute inset-0 flex items-center justify-center">
                    <span className={`text-2xl font-bold ${getScoreColor(metric.score)}`}>
                      {metric.score}
                    </span>
                  </div>
                </div>
                <h3 className="text-lg font-bold text-gray-900 mb-2">{metric.name}</h3>
                <div className="flex items-center justify-center space-x-2 text-sm">
                  <span className="text-green-600 font-medium">{metric.trend}</span>
                  <span className="text-gray-500">•</span>
                  <span className="text-gray-600">{metric.issues} 问题</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 治理规则 */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">治理规则</h2>
          <button className="bg-blue-600 text-white px-6 py-3 rounded-xl hover:bg-blue-700 transition-colors flex items-center space-x-2 font-medium">
            <Plus className="w-5 h-5" />
            <span>创建规则</span>
          </button>
        </div>

        <div className="space-y-4">
          {governanceRules.map((rule, index) => (
            <div
              key={rule.id}
              className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 hover:shadow-xl transition-all duration-300"
            >
              <div className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-bold text-gray-900">{rule.name}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(rule.status)}`}>
                        {getStatusText(rule.status)}
                      </span>
                      <span className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs">
                        {rule.type}
                      </span>
                    </div>
                    <p className="text-gray-600 mb-4">{rule.description}</p>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <p className="text-xs text-gray-500 mb-1">覆盖率</p>
                        <div className="flex items-center space-x-2">
                          <div className="flex-1 bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full"
                              style={{ width: rule.coverage }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium text-gray-900">{rule.coverage}</span>
                        </div>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500 mb-1">最后更新</p>
                        <p className="text-sm font-medium text-gray-900">{rule.lastUpdated}</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3 ml-6">
                    <button className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors">
                      编辑
                    </button>
                    <Link
                      href={`/governance/rule/${rule.id}`}
                      className="text-blue-600 hover:text-blue-700 font-medium flex items-center space-x-1 transition-colors text-sm"
                    >
                      <span>详情</span>
                      <ArrowRight className="w-4 h-4" />
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

'use client'

import { Users, UserCheck, UserX, Shield, Eye, Edit, Trash2, Plus, Search, Filter } from 'lucide-react'
import { useState } from 'react'

export default function UserPermissionsPage() {
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedRole, setSelectedRole] = useState('all')

  const users = [
    {
      id: 1,
      name: '张三',
      email: 'zhang<PERSON>@gov.cn',
      department: '公安局',
      role: '数据管理员',
      status: 'active',
      lastLogin: '2024-01-15 14:30',
      permissions: ['人口数据查询', '数据导出', '用户管理'],
      avatar: 'ZS'
    },
    {
      id: 2,
      name: '李四',
      email: '<EMAIL>',
      department: '市场监管局',
      role: '业务用户',
      status: 'active',
      lastLogin: '2024-01-15 10:20',
      permissions: ['企业信息查询', '数据查看'],
      avatar: 'LS'
    },
    {
      id: 3,
      name: '王五',
      email: '<EMAIL>',
      department: '自然资源局',
      role: '系统管理员',
      status: 'inactive',
      lastLogin: '2024-01-10 16:45',
      permissions: ['系统配置', '用户管理', '权限分配', '数据管理'],
      avatar: 'WW'
    },
    {
      id: 4,
      name: '赵六',
      email: '<EMAIL>',
      department: '环保局',
      role: '数据分析师',
      status: 'active',
      lastLogin: '2024-01-15 09:15',
      permissions: ['环境数据查询', '数据分析', '报表生成'],
      avatar: 'ZL'
    }
  ]

  const roles = ['all', '系统管理员', '数据管理员', '业务用户', '数据分析师']

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.department.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesRole = selectedRole === 'all' || user.role === selectedRole
    return matchesSearch && matchesRole
  })

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-700'
      case 'inactive': return 'bg-red-100 text-red-700'
      case 'pending': return 'bg-yellow-100 text-yellow-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '活跃'
      case 'inactive': return '非活跃'
      case 'pending': return '待激活'
      default: return '未知'
    }
  }

  const getRoleColor = (role: string) => {
    switch (role) {
      case '系统管理员': return 'bg-purple-100 text-purple-700'
      case '数据管理员': return 'bg-blue-100 text-blue-700'
      case '业务用户': return 'bg-green-100 text-green-700'
      case '数据分析师': return 'bg-orange-100 text-orange-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  return (
    <div className="space-y-8">
      {/* 页面标题 */}
      <div>
        <h1 className="text-4xl font-bold text-gray-900 mb-4">用户权限管理</h1>
        <p className="text-xl text-gray-600">管理用户账户、角色分配和权限设置</p>
      </div>

      {/* 用户统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">总用户数</p>
              <p className="text-3xl font-bold text-gray-900">{users.length}</p>
            </div>
            <Users className="w-8 h-8 text-indigo-500" />
          </div>
        </div>
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">活跃用户</p>
              <p className="text-3xl font-bold text-gray-900">{users.filter(u => u.status === 'active').length}</p>
            </div>
            <UserCheck className="w-8 h-8 text-green-500" />
          </div>
        </div>
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">非活跃用户</p>
              <p className="text-3xl font-bold text-gray-900">{users.filter(u => u.status === 'inactive').length}</p>
            </div>
            <UserX className="w-8 h-8 text-red-500" />
          </div>
        </div>
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 mb-1">管理员</p>
              <p className="text-3xl font-bold text-gray-900">{users.filter(u => u.role.includes('管理员')).length}</p>
            </div>
            <Shield className="w-8 h-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/20">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="搜索用户..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              />
            </div>
            <div className="flex items-center space-x-2">
              <Filter className="w-5 h-5 text-gray-400" />
              <select
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
                className="border border-gray-200 rounded-lg px-3 py-2 focus:ring-2 focus:ring-indigo-500 focus:border-transparent"
              >
                {roles.map(role => (
                  <option key={role} value={role}>
                    {role === 'all' ? '全部角色' : role}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <button className="flex items-center space-x-2 bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
            <Plus className="w-5 h-5" />
            <span>添加用户</span>
          </button>
        </div>
      </div>

      {/* 用户列表 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 overflow-hidden">
        <div className="p-6 border-b border-gray-100">
          <h3 className="text-lg font-bold text-gray-900">用户列表</h3>
        </div>
        <div className="divide-y divide-gray-100">
          {filteredUsers.map((user) => (
            <div
              key={user.id}
              className="p-6 hover:bg-gray-50/50 transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 flex-1">
                  <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                    {user.avatar}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className="text-lg font-medium text-gray-900">{user.name}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
                        {getStatusText(user.status)}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                        {user.role}
                      </span>
                    </div>
                    <div className="flex items-center space-x-6 text-sm text-gray-500 mb-2">
                      <span>{user.email}</span>
                      <span>{user.department}</span>
                      <span>最后登录: {user.lastLogin}</span>
                    </div>
                    <div className="flex flex-wrap gap-2">
                      {user.permissions.map((permission) => (
                        <span key={permission} className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded-full">
                          {permission}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button className="text-indigo-600 hover:text-indigo-700 p-2 rounded-lg hover:bg-indigo-50 transition-colors">
                    <Eye className="w-5 h-5" />
                  </button>
                  <button className="text-blue-600 hover:text-blue-700 p-2 rounded-lg hover:bg-blue-50 transition-colors">
                    <Edit className="w-5 h-5" />
                  </button>
                  <button className="text-red-600 hover:text-red-700 p-2 rounded-lg hover:bg-red-50 transition-colors">
                    <Trash2 className="w-5 h-5" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

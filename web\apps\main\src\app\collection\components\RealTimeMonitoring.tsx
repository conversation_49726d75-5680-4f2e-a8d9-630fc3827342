'use client'

import {
  Activity,
  HardDrive,
  Wifi
} from 'lucide-react'

export default function RealTimeMonitoring() {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900">实时监控</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white/80 rounded-lg p-4 border border-white/20">
          <div className="flex items-center space-x-2 mb-2">
            <Activity className="w-5 h-5 text-green-500" />
            <h4 className="font-semibold text-gray-900">系统负载</h4>
          </div>
          <p className="text-2xl font-bold text-green-600">23%</p>
          <p className="text-sm text-gray-600">CPU使用率</p>
        </div>
        <div className="bg-white/80 rounded-lg p-4 border border-white/20">
          <div className="flex items-center space-x-2 mb-2">
            <HardDrive className="w-5 h-5 text-blue-500" />
            <h4 className="font-semibold text-gray-900">存储使用</h4>
          </div>
          <p className="text-2xl font-bold text-blue-600">67%</p>
          <p className="text-sm text-gray-600">磁盘占用率</p>
        </div>
        <div className="bg-white/80 rounded-lg p-4 border border-white/20">
          <div className="flex items-center space-x-2 mb-2">
            <Wifi className="w-5 h-5 text-indigo-500" />
            <h4 className="font-semibold text-gray-900">网络流量</h4>
          </div>
          <p className="text-2xl font-bold text-indigo-600">1.2GB/s</p>
          <p className="text-sm text-gray-600">实时传输速率</p>
        </div>
      </div>
    </div>
  )
}
